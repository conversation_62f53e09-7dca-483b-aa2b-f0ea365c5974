<template>
  <BaseItem title="粗细">
    <n-select
      :value="value"
      filterable
      placeholder="选择文字粗细"
      size="small"
      :options="FontWeightOptions"
      @update:value="onChange"
    />
  </BaseItem>
</template>

<script setup lang="ts">
import BaseItem from "../index.vue";

const FontWeightOptions = [
  { label: "normal", value: "normal" },
  { label: "bold", value: "bold" },
  { label: "bolder", value: "bolder" },
  { label: "lighter", value: "lighter" },
  { label: "100", value: "100" },
  { label: "200", value: "200" },
  { label: "300", value: "300" },
  { label: "400", value: "400" },
  { label: "500", value: "500" },
  { label: "600", value: "600" },
  { label: "700", value: "700" },
  { label: "800", value: "800" },
  { label: "900", value: "900" }
];
const emit = defineEmits<{
  "update:value": [value: string];
}>();

defineProps<{
  value?: string | number | null;
}>();

const onChange = (value: string) => {
  emit("update:value", value);
};
</script>

<style></style>
