<template>
  <n-modal
    v-model:show="isVisible"
    preset="dialog"
    title="新增条件配置"
    size="huge"
    :bordered="false"
    :show-icon="false"
    :close-on-esc="false"
    :maskClosable="false"
    positive-text="确认"
    negative-text="取消"
    @positive-click="submit"
    @negative-click="hide"
    style="margin-top: 20vh; width: 600px"
  >
    <n-button size="small" type="primary" class="mb-5" @click="addGroupData">新增配置</n-button>

    <n-scrollbar style="max-height: 300px" v-if="groupConditionForm.length">
      <n-collapse :trigger-areas="['arrow', 'main']">
        <n-collapse-item v-for="(data, dataIndex) in groupConditionForm" :key="`data-${dataIndex}`">
          <template #header>
            <span class="w-full cursor-pointer">{{ `key：${data.column || "--"}` }}</span>
          </template>
          <template #header-extra>
            <div class="flex">
              <n-select
                v-model:value="data.dataType"
                :options="DataTypeOptions"
                size="small"
                class="w-20 flex-shrink-0 mx-1"
              >
              </n-select>
              <n-tooltip trigger="hover">
                <template #trigger>
                  <n-button
                    text
                    style="font-size: 18px"
                    type="primary"
                    class="mx-1"
                    @click="showDataBindModal(dataIndex)"
                  >
                    <n-icon :component="Settings" />
                  </n-button>
                </template>
                配置数据key
              </n-tooltip>

              <n-tooltip trigger="hover" v-if="data.dataType !== 'text'">
                <template #trigger>
                  <n-button
                    text
                    style="font-size: 18px"
                    type="info"
                    class="mx-1"
                    @click="addItem(data)"
                  >
                    <n-icon :component="Add" />
                  </n-button>
                </template>
                新增条件配置
              </n-tooltip>

              <n-button
                text
                style="font-size: 18px"
                type="error"
                class="mx-1"
                @click="deleteSaveDataBind(data, dataIndex)"
              >
                <n-icon :component="Delete" />
              </n-button>
            </div>
          </template>
          <n-scrollbar style="max-height: 255px" v-if="data.dataType !== 'text'">
            <n-form ref="formRef" :model="data.conditions" label-placement="left" class="mr-2">
              <n-form-item v-for="(item, index) in data.conditions" :key="index">
                <n-input-group>
                  <n-input-number
                    v-model:value="item.threshold"
                    class="w-30 flex-shrink-0"
                    clearable
                    placeholder="阈值"
                  />
                  <n-select
                    v-model:value="item.comparison"
                    :options="ComparisonOptions"
                    class="w-26 flex-shrink-0"
                  >
                  </n-select>
                  <n-select
                    v-model:value="item.style.type"
                    :options="StyleOptions"
                    class="w-40 flex-shrink-0"
                    clearable
                    placeholder="样式类型"
                    @update:value="onStyleTypeChange($event, item)"
                  >
                  </n-select>

                  <n-input
                    v-if="getInputType(item.style.type, 'string')"
                    v-model:value="item.style.data"
                    clearable
                    placeholder="请输入样式"
                  />

                  <n-color-picker
                    v-else-if="getInputType(item.style.type, 'color')"
                    v-model:value="item.style.data"
                    :show-preview="true"
                    :modes="['hex', 'rgb']"
                  />
                  <div v-else-if="getInputType(item.style.type, 'image')" class="flex flex-1">
                    <n-input v-model:value="item.style.data" clearable placeholder="请上传图标">
                      <template #suffix>
                        <n-upload
                          accept="image/*"
                          style="height: 22px; width: 22px; margin-left: 5px; margin-bottom: 2px"
                          clearable
                          :default-upload="false"
                          :show-file-list="false"
                          @change="onUploadChange($event, item)"
                        >
                          <n-icon :component="CloudUpload" size="20" class="cursor-pointer" />
                        </n-upload>
                      </template>
                    </n-input>

                    <div
                      class="bg-#424247 flex justify-center items-center ml-2 w-34px h-full flex-shrink-0 rounded-3px"
                    >
                      <n-image width="30" :src="urlPrefix + item.style.data" />
                    </div>
                  </div>
                  <n-input-number
                    v-else-if="getInputType(item.style.type, 'number')"
                    v-model:value="item.style.data"
                    class="flex-1"
                    clearable
                    placeholder="请输入样式"
                  />

                  <n-select
                    v-else-if="getInputType(item.style.type, 'select')"
                    v-model:value="item.style.data"
                    :options="StyleSelectOptions"
                    clearable
                    placeholder="请选择样式"
                  >
                  </n-select>
                </n-input-group>
                <n-button text style="font-size: 18px" class="mx-1" @click="addItem(data, index)">
                  <n-icon :component="Add" />
                </n-button>
                <n-button text style="font-size: 18px" @click="removeItem(index, data)">
                  <n-icon :component="Subtract" />
                </n-button>
              </n-form-item>
            </n-form>
          </n-scrollbar>
        </n-collapse-item>
      </n-collapse>
    </n-scrollbar>
    <n-empty description="暂无数据" v-else> </n-empty>

    <DataBindModal ref="dataBindRef" @onValueUpdate="onValueUpdate"></DataBindModal>
  </n-modal>
</template>

<script setup lang="ts">
import { computed, type ComputedRef, ref, toRaw } from "vue";
import { type SelectOption, type UploadFileInfo, useDialog } from "naive-ui";

import DataBindModal from "@/components/Common/Modal/DataBindConfig/index.vue";
import type { ICondition, IMetaItem, IObjDataBindInfo, IStyleOption } from "@/types";
import { Add, CloudUpload, Delete, Settings, Subtract } from "@/utils/components/icons";
import {
  ComparisonOptions,
  DataTypeOptions,
  StyleSelectMap,
  StyleTypeLinkOptions,
  StyleTypeNodeOptions
} from "@/utils/constant";
import { uploadFile } from "@/utils/http/apis";
import { getImageUrl } from "@/utils/tools";

const emit = defineEmits<{
  onValueUpdate: [IObjDataBindInfo[]];
}>();

const urlPrefix = getImageUrl();

const dialog = useDialog();
const iconInfo = ref<IMetaItem>();
const isVisible = ref(false);
const activeIndex = ref<number>(0);

// const domId = ref<string>();
const styleTypeOption = ref<IStyleOption>();
const dataBindRef = ref<InstanceType<typeof DataBindModal> | null>(null);

const groupConditionForm = ref<IObjDataBindInfo[]>([]);

const initData = (val?: IObjDataBindInfo[]) => {
  groupConditionForm.value = val ? window.structuredClone(toRaw(val)) : [];
};

const StyleOptions = computed(() => {
  if (iconInfo.value?.compClass === "link") {
    return StyleTypeLinkOptions;
  } else {
    return StyleTypeNodeOptions;
  }
});

// 下拉框选项
const StyleSelectOptions: ComputedRef<SelectOption[]> = computed(() => {
  if (!styleTypeOption.value) return [];
  return StyleSelectMap[styleTypeOption.value.value];
});

const getInputType = (conditionType: string | null, inputType = "string") => {
  let type = "string";
  StyleTypeNodeOptions.forEach((ele) => {
    if (ele.value === conditionType) {
      type = ele.type;
    }
  });

  return type === inputType;
};

const onUploadChange = async (options: { file: UploadFileInfo }, item: ICondition) => {
  const file = options.file.file;
  if (!file) return;
  const formData = new FormData();
  formData.append("file", file);

  if (file.type === "image/svg+xml") {
    const reader = new FileReader();
    reader.onload = function (e) {
      if (e.target) {
        // item.style.svgData = e.target.result as string; // 这里是SVG文件的内容
      }
    };
    reader.readAsText(new Blob([file])); // 以文本格式读取文件
  }

  uploadFile(formData)
    .then((res) => {
      item.style.data = res;
    })
    .catch(() => {
      window.$message.error("上传失败");
    });
};

const onStyleTypeChange = (value: string, item: ICondition) => {
  StyleTypeNodeOptions.forEach((ele) => {
    if (ele.value === value) {
      styleTypeOption.value = ele;
    }
  });

  // 将当前图元设为默认数据
  if (value === "background") {
    item.style.data = iconInfo.value?.objImg;
    item.style.svgData = "";
  } else {
    item.style.data = null;
  }

  // styleTypeOption.value = options;
};
const showDataBindModal = (index: number) => {
  activeIndex.value = index;
  dataBindRef.value?.show();
};

const onValueUpdate = ({ key, id }: { key: string | null; id: number | null }) => {
  groupConditionForm.value[activeIndex.value].column = key || "";
  groupConditionForm.value[activeIndex.value].extractId = id;
};

const addGroupData = () => {
  const params: IObjDataBindInfo = {
    column: "",
    conditions: [],
    extractId: null,
    dataType: "text",
    domId: ""
  };

  groupConditionForm.value.push(params);
};

const addItem = (item: IObjDataBindInfo, index?: number) => {
  const newItem = {
    tagName: null,
    comparison: "=",
    threshold: 0,
    style: {
      data: null,
      type: null
    }
  };
  if (index === undefined) {
    item.conditions?.push(newItem);
  } else {
    item.conditions?.splice(index + 1, 0, newItem);
  }
};

const removeItem = (index: number, item: IObjDataBindInfo) => {
  item.conditions?.splice(index, 1);
};

const deleteSaveDataBind = async (data: IObjDataBindInfo, index: number) => {
  dialog.warning({
    title: "警告",
    content: "确定删除当前数据吗？",
    positiveText: "确定",
    negativeText: "取消",
    maskClosable: false,
    closeOnEsc: false,
    onPositiveClick: async () => {
      groupConditionForm.value.splice(index, 1);
      window.$message.success("删除成功");
    },
    onAfterLeave: () => {}
  });
};

const show = (val?: IObjDataBindInfo[]) => {
  isVisible.value = true;
  initData(val);
};

const hide = () => {
  isVisible.value = false;
};

const submit = () => {
  emit("onValueUpdate", groupConditionForm.value);
  hide();
  return false;
};

defineExpose({
  show
});
</script>
