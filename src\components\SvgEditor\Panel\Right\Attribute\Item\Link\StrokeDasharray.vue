<template>
  <BaseItem title="间距" tooltip="例如：10,5">
    <n-input
      :value="data"
      size="small"
      placeholder="请输入间距，例如：10,5"
      @update:value="updateStyle"
    />
  </BaseItem>
</template>

<script setup lang="ts">
import BaseItem from "@/components/SvgEditor/Panel/Right/Attribute/Item/index.vue";
defineProps<{
  data?: string | number;
}>();

const emit = defineEmits<{
  "update:value": [value: string];
}>();

const updateStyle = (value: string) => {
  emit("update:value", value);
};
</script>

<style scoped></style>
