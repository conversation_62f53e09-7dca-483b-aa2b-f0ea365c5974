import type { SVGCommand } from "svg-pathdata/dist/types";

import type { ISublayerItem } from "./map";
import type { IMetaIconDataBind } from "./meta";
import type { IMouseEvent } from "./mouse-event";

export interface ISize {
  width: number;
  height: number;
}

export interface IPosition {
  x: number;
  y: number;
}
export interface IMatrix {
  a: number;
  b: number;
  c: number;
  d: number;
  e: number;
  f: number;
}

export interface IOriginalNode {
  nodeId?: string;
  mapId: string;
  domId: string;
  nodeType: string;
  compClass: string;
  nodePosition: string; // 12,5
  nodeSize: string; // 36*20
  rotate: number;
  nodeStyles: string;
  nodeText: string;
  fontSize: string;
  fontColor: string;
  textPosition: string;
  textStyles: string;
  bindData: Record<string, string>;
  bindMap: any;
  metaData: Record<string, string> | null;
  sublayerList: ISublayerItem[];
  svgData?: string;
  script?: string;
  topoObjDataBindInfos?: IMetaIconDataBind[];
  mouseEvent?: IMouseEvent[];
  objImg?: string;
}

export interface ISourceNode extends IOriginalNode {
  nodeId: string;
  bindLink?: any;
  bindSubLink?: string | null;
  updatedBy?: string | null;
  updatedTime?: string | null;
}

export interface INode extends ISourceNode {
  isMerge?: boolean;
  x: number;
  y: number;
  width: number;
  height: number;
  style: Record<string, string | number>;
  textStyle: Record<string, string | number>;
  selected?: boolean;
  locked?: boolean;
  groupId?: string[];
  zIndex?: number;
  targets: ILink[];
  sources: ILink[];
}

export interface IOriginalLink {
  linkId?: string;
  domId: string;
  mapId: string;
  linkType: string;
  dashedLink: string;
  compClass: string;
  linkPath: string;
  linkWidth: number;
  linkStyles: string;
  linkAnimations: Record<string, string>;
  fromObj: string;
  endObj: string | null;
  bindData: Record<string, string>;
  bindMap: any;
  metaData: Record<string, any> | null;
  sublayerList: ISublayerItem[];
  script?: string;
  mouseEvent?: IMouseEvent[];
}

export interface ISourceLink extends IOriginalLink {
  linkId: string;
  updatedBy?: string | null;
  updatedTime?: string | null;
}

export interface ILink extends ISourceLink {
  isMerge?: boolean;
  style: Record<string, string | number>;
  //   pathArray: any[];
  selected?: boolean;
  locked?: boolean;
  x: number;
  y: number;
  width: number;
  height: number;
  transform: {
    x: number;
    y: number;
  };
  points: SVGCommand[];
  groupId?: string[];
  source?: INode | null;
  target?: INode | null;
}

export interface INodeLinkSource {
  nodes: ISourceNode[];
  links: ISourceLink[];
}

export type IImportType = "import" | "importPart" | "importAll";

export type IObjDataBindInfo = Omit<IMetaIconDataBind, "id" | "objType">;
export interface IGroupDataBind {
  type: string;
  key: string | null;
  detailId: string | null;
  nodeLinkId?: string;
  objDataBindInfo?: IObjDataBindInfo[];
}
export interface IGroupData {
  mapId?: string;
  groupId: string;
  groupName: string;
  groupDescription: string;
  dataIds: string[];
  nodes?: INode[];
  links?: ILink[];
  bindData?: IGroupDataBind[];
  selected?: boolean;
  groupType?: number;
}

export interface IGroupDataModel {
  mapId: string;
  groupId?: string;
  groupName: string;
  groupDescription?: string;
  bindData?: IGroupDataBind[];
  topoMapsGroupDataList: {
    dataId: string;
    dataType: "node" | "link";
  }[];
}

export interface ICombineSvgData {
  mapId: string;
  nodes: INode[];
  links: ILink[];
  size: { width: number; height: number };
  x: number;
  y: number;
  selected?: boolean;
  tx: number;
  ty: number;
}
