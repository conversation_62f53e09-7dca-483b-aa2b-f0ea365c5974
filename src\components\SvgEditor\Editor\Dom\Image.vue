<template>
  <!-- <foreignObject :width="data.width" :height="data.height"> -->
  <!-- <div>
      <img :style="style" :data-url="props.data.style.image" :src="getNodeImage(props.data)" />
    </div> -->
  <!-- </foreignObject> -->
  <image :width="data.width" :height="data.height" :style="data.style" :href="getNodeImage(data)" />
  <NodeText :data="data" v-if="data.nodeText" />
</template>

<script setup lang="ts">
import type { INode } from "@/types";
import { getNodeImage } from "@/utils/tools";

import NodeText from "./NodeText.vue";

const props = defineProps<{
  data: INode;
}>();

// const style = computed(() => {
// const bg = props.data.style.image ? `url(${getNodeImage(props.data)})` : props.data.style.fill;
// return {
//   ...props.data.style
// backgroundImage: `url(${getNodeImage(props.data)})`,
// backgroundColor: (props.data.style.fill as string) || "transparent",
// backgroundSize: props.data.style["background-size"],
// backgroundRepeat: "no-repeat",
// backgroundPosition: props.data.style["background-position"],
// width: "100%",
// height: "100%"
// };
// });
</script>
