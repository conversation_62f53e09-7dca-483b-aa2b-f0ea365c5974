<template>
  <image :width="data.width" :height="data.height" :style="data.style" :href="getNodeImage(data)" />
  <NodeText :data="data" v-if="data.nodeText" />
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { INode } from "@/types";
import { getNodeImage } from "@/utils/tools";
import { getImageUrl } from "@/utils/tools";

import NodeText from "./NodeText.vue";

const props = defineProps<{
  data: INode;
}>();

// 类型定义
interface MetaData {
  powerType?: string;
  powerTypeByCustom?: string;
  volt?: string;
  type?: string;
  xfmrNo?: string;
  stationType?: string;
  switchNo?: string;
  switchRunningNo?: string;
  [key: string]: any;
}

interface ExtraMetaData {
  switch: Array<{ status?: number }>;
}

interface NodeStylesObj {
  image?: string;
  line?: {
    length: number;
    rotate: number;
    fill?: string;
  };
  dynamicColor?: string;
  [key: string]: any;
}

interface NodeStylesResult extends NodeStylesObj {
  filter?: string;
  isShowDivid: boolean;
  dividColor: string;
}

// 过滤器颜色列表
const filterColorList: Record<string, string> = {
  green:
    "brightness(0) saturate(100%) invert(23%) sepia(99%) saturate(1439%) hue-rotate(100deg) brightness(107%) contrast(106%)",
  pink: "brightness(0) saturate(100%) invert(29%) sepia(37%) saturate(5527%) hue-rotate(310deg) brightness(100%) contrast(102%)",
  black:
    "brightness(0) saturate(100%) invert(0%) sepia(100%) saturate(0%) hue-rotate(234deg) brightness(96%) contrast(107%)",
  red: "brightness(0) saturate(100%) invert(10%) sepia(81%) saturate(6148%) hue-rotate(347deg) brightness(92%) contrast(98%)",
  orange:
    "brightness(0) saturate(100%) invert(32%) sepia(79%) saturate(3277%) hue-rotate(1deg) brightness(102%) contrast(104%)",
  blue: "brightness(0) saturate(100%) invert(36%) sepia(81%) saturate(4907%) hue-rotate(223deg) brightness(99%) contrast(99%)"
};

// 颜色列表
const colorList: Record<string, string> = {
  red: "#ff0000",
  green: "#00ff00",
  blue: "#0000ff",
  orange: "#ffa500",
  yellow: "#ffff00",
  purple: "#800080",
  black: "#000000",
  white: "#ffffff",
  gray: "#808080"
};

// 工具函数：绘制变电站图标（占位符实现）
const drawStationIcon = (xfmrMvarates: any[]): string => {
  // 这里应该根据实际业务逻辑实现
  // 暂时返回默认图标
  return "500KV.svg";
};

// 工具函数：处理开关分排判断
const processSwitchDivision = (
  switchNo: string | undefined,
  switchRunningNo: string | undefined,
  extraMetaData: ExtraMetaData
): boolean => {
  if (!switchNo || !switchRunningNo) return false;

  const switchNos = switchNo.split(",").map((ele: string) => ele.split("_")[0]);
  const switchRunningNos = switchRunningNo.split(",");

  let isShowDivid = false;
  switchNos.forEach((ele: string, index: number) => {
    if (switchRunningNos.includes(ele)) {
      isShowDivid = !!(isShowDivid || +(extraMetaData.switch[index]?.status || 0));
    }
  });

  return isShowDivid;
};

// 工具函数：获取电站图标
const getPowerStationIcon = (powerType: string, powerTypeByCustom: string | undefined): string => {
  if (powerType === "WIND") {
    const windType = powerTypeByCustom === "海" ? "WIND" : "LAND";
    return `powerPlant_s_${windType}.svg`;
  }
  return `powerPlant_s_${powerType}.svg`;
};

// 工具函数：获取变电站图标
const getTransStationIcon = (
  volt: string | undefined,
  stationType: string | undefined,
  xfmrNo: string | undefined,
  metaData: MetaData
): string => {
  const highVoltLevels = ["_500KV", "_1000KV", "_1750KV", "_3000KV"];

  if (volt && highVoltLevels.includes(volt)) {
    const xfmrMvarates: any[] = [];

    if (xfmrNo) {
      xfmrNo.split(",").forEach((ele: string) => {
        const xfmrMvarate = `xfmrMvarate${ele}`;
        const xfmrRunningStatus = `xfmrRunningStatus${ele}`;
        const status = metaData[xfmrRunningStatus];

        if (status === "RUNNING") {
          xfmrMvarates.push(metaData[xfmrMvarate]);
        }
      });
    }

    return drawStationIcon(xfmrMvarates);
  }

  if (volt === "_220KV" || volt === "_110KV") {
    return stationType === "小电源" ? "220KVxdy.svg" : "220kV-01.svg";
  }

  return "";
};

// 工具函数：处理图片路径
const processImagePath = (img: string): string => {
  if (img.startsWith("data:image") || img.startsWith("img/")) {
    return img;
  }

  const fullPath = "/ftp/icon/" + img;
  return getImageUrl() + fullPath;
};

// 主要的计算属性
const nodeStyles = computed((): NodeStylesResult => {
  const { nodeStyles: nodeStylesStr, metaData: rawMetaData } = props.data;

  // 安全地处理metaData和extraMetaData
  const metaData = (rawMetaData || {}) as MetaData;
  const extraMetaData = (props.data as any).extraMetaData || ({ switch: [] } as ExtraMetaData);

  // 解析节点样式
  const nodeStylesObj: NodeStylesObj = nodeStylesStr ? JSON.parse(nodeStylesStr) : {};

  let filter: string | undefined;

  // 解构元数据
  const {
    powerType,
    powerTypeByCustom,
    volt,
    type,
    xfmrNo,
    stationType,
    switchNo,
    switchRunningNo
  } = metaData;

  // 处理开关分排判断
  const isShowDivid = processSwitchDivision(switchNo, switchRunningNo, extraMetaData);

  // 确定图标
  let img = "";

  if (type === "PowerStation" && powerType) {
    // 电站图标处理
    img = getPowerStationIcon(powerType, powerTypeByCustom);
    filter = volt === "_500KV" ? filterColorList.red : filterColorList.black;
  } else if (type === "TNode") {
    // T节点图标
    img = require("../../assets/images/meta-icons/power/t.svg");
  } else if (type === "ExchStation") {
    // 交换站图标
    img = "powerPlant_s_ExchStation.svg";
  } else if (type === "TransStation") {
    // 变电站图标
    img = getTransStationIcon(volt, stationType, xfmrNo, metaData);
  }

  // 处理图片路径
  if (img) {
    nodeStylesObj.image = processImagePath(img);
  } else {
    // 处理默认图片
    if (nodeStylesObj.image === "/ftp/icon/分区.png") {
      nodeStylesObj.image = "/ftp/icon/fq.png";
    }
    nodeStylesObj.image = nodeStylesObj.image
      ? getImageUrl() + nodeStylesObj.image
      : require("././images/addenvironment.png");
  }

  // 确保line属性存在
  if (!nodeStylesObj.line) {
    nodeStylesObj.line = {
      length: 0,
      rotate: 0
    };
  }

  // 处理动态颜色
  if (nodeStylesObj.dynamicColor) {
    filter = filterColorList[nodeStylesObj.dynamicColor];
  }

  // 返回最终结果
  return {
    ...nodeStylesObj,
    filter,
    isShowDivid,
    dividColor: nodeStylesObj.line?.fill || colorList[nodeStylesObj.dynamicColor || ""] || "#000000"
  };
});
</script>
