import { ref } from "vue";
import * as d3 from "d3";
import type { UploadFileInfo } from "naive-ui";

import { useDataStore, useMapStore } from "@/stores";
import type { ICompanyData, ILink, IOriginalNode } from "@/types";
import { BaseNode } from "@/utils/constant";
import request from "@/utils/http/";
import { addNodeLinkList } from "@/utils/http/apis";
import { setNodesSelected } from "@/utils/tools";

interface IInnerNodeOption {
  width: number;
  height: number;
  gap: number;
}

interface IInnerNode extends IOriginalNode {
  x: number;
  y: number;
  width: number;
  height: number;
}

export const useInnerNode = () => {
  const innerNodes = ref<IInnerNode[]>([]);
  const fileList = ref<UploadFileInfo[]>([]);
  const width = ref<number>(100);
  const height = ref<number>(100);
  const gap = ref<number>(40);

  let companyDataList: ICompanyData[] = [];
  const mapStore = useMapStore();
  const dataStore = useDataStore();

  const importCompanyData = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);

    companyDataList = await request.post<ICompanyData[]>({
      url: "/topoEdit/parseExcel",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data"
      }
    });
  };

  /**
   *
   *   如果innerNodes只有一个，直接放在中心
   *   innerNodes两个，放在中心左右
   *   innerNodes三个，放在中心上下左
   *   innerNodes四个，放在中心上下左右
   *   如果横向放不下，换行
   * @param bbox
   * @param innerNodeList
   * @param width
   * @param height
   * @param gap
   * @returns
   */
  const generateInnerNode = (companyItem: ICompanyData, bbox: DOMRect, mapId: string) => {
    const centerX = bbox.x + bbox.width / 2;
    const centerY = bbox.y + bbox.height / 2;
    const nodes = companyItem.dataList.map((deviceInfo, index) => {
      let x, y;
      switch (companyItem.dataList.length) {
        case 1:
          x = centerX - width.value / 2;
          y = centerY - height.value / 2;
          break;
        case 2:
          x = centerX - width.value - gap.value / 2 + index * (width.value + gap.value);
          y = centerY - height.value / 2;
          break;
        case 3:
          if (index === 0) {
            x = centerX - width.value / 2;
            y = centerY - height.value - gap.value / 2;
          } else {
            x = centerX - width.value - gap.value / 2 + (index - 1) * (width.value + gap.value);
            y = centerY + gap.value / 2;
          }
          break;
        case 4:
          x = centerX - width.value - gap.value / 2 + (index % 2) * (width.value + gap.value);
          y =
            centerY -
            height.value -
            gap.value / 2 +
            Math.floor(index / 2) * (height.value + gap.value);
          break;
        default:
          x = centerX;
          y = centerY;
      }
      return {
        ...BaseNode,
        mapId,
        x,
        y,
        width: width.value,
        height: height.value,
        nodeType: deviceInfo.objType,
        nodePosition: `${x},${y}`,
        nodeSize: `${width.value}*${height.value}`,
        domId: deviceInfo.addressCode,
        metaData: {
          ...deviceInfo,
          type: deviceInfo.nodeType
        }
      };
    });
    return nodes;
  };

  const addInnerNodes = async () => {
    const { nodeList } = await addNodeLinkList({ nodeList: innerNodes.value, linkList: [] });
    dataStore.nodeLinkListByImport.nodeList = nodeList;
    mapStore.isMoveToSublayerVisible = true;
    removeInnerNodes();
  };
  /**
   * 根据化工厂生成点位
   */
  const drawInnerNodes = async () => {
    const dataStore = useDataStore();
    const mapStore = useMapStore();
    const mapId = mapStore.mapInfo?.mapId;
    if (!mapId) return;
    removeInnerNodes();
    dataStore.linksAll.forEach((link) => {
      const bbox = d3.select<SVGPathElement, ILink>(`#link_${link.linkId}`).node()?.getBBox();
      if (!bbox) return;

      const companyData = companyDataList.find((company) => {
        const companyCode = link.domId.split("_")[1];
        return company.code === companyCode;
      });

      if (!companyData) return;

      const list = generateInnerNode(companyData, bbox, mapId);

      innerNodes.value.push(...list);
    });
    drawPreviewInnerNodes();
  };

  const drawPreviewInnerNodes = () => {
    d3.select("#map")
      .selectAll<SVGRectElement, IInnerNode>(".inner-node")
      .data(innerNodes.value, (d) => d.metaData!.nodeName)
      .join("rect")
      .attr("class", "inner-node")
      .attr("transform", (d) => `translate(${d.x},${d.y})`)
      .attr("width", (d) => d.width)
      .attr("height", (d) => d.height)
      .attr("fill", "white");
  };

  const removeInnerNodes = () => {
    innerNodes.value = [];
    d3.select("#map").selectAll(".inner-node").remove();
  };

  /**
   * 导入数据 并生成点位图
   * @param options
   * @returns
   */
  const onUploadChange = async (options: { file: UploadFileInfo }) => {
    innerNodes.value = [];
    const file = options.file.file;
    if (!file) return;

    await importCompanyData(file);
    fileList.value = [];

    drawInnerNodes();
  };
  return {
    width,
    height,
    gap,
    fileList,
    onUploadChange,
    addInnerNodes,
    drawPreviewInnerNodes,
    drawInnerNodes,
    removeInnerNodes,
    useInnerNode
  };
};
