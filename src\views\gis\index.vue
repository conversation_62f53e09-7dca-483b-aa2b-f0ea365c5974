<template>
  <div id="map"></div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import L from "leaflet";

import "leaflet/dist/leaflet.css";

const tileLayers = {
  地形: L.tileLayer(`${import.meta.env.VITE_GIS_TILE_URL}/tiles/terrain/{z}/{x}/{y}.png`),
  卫星: L.tileLayer(`${import.meta.env.VITE_GIS_TILE_URL}/tiles/zd/{z}/{x}/{y}.png`)
};

const pos = [118.776969, 32.043004];

const popupWidth = 450;
const popupHeight = 300;
onMounted(() => {
  const map = L.map("map", {
    layers: [tileLayers["地形"]],
    zoomControl: false,
    doubleClickZoom: false,
    center: [pos[1], pos[0]],
    zoom: 10
  });
  // 去除右下角的 Leaflet 字样
  map.attributionControl.setPrefix("");
  // 创建marker
  const maker = L.marker({
    lat: pos[1],
    lng: pos[0]
  }).addTo(map);
  // 设置缩放比例 为13
  map.setZoom(14);

  // 创建一个弹窗，内部内容为一个div标签
  const videoPopup = L.popup({
    closeButton: false,
    closeOnClick: false,
    autoClose: false,
    closeOnEscapeKey: false,
    maxWidth: popupWidth,
    maxHeight: popupHeight
  })
    .setLatLng([pos[1], pos[0]])
    // 设置宽高
    .setContent(
      `
      <div style="width: ${popupWidth}px; height: ${popupHeight}px;">
        <video
          src="https://www.runoob.com/try/demo_source/movie.mp4"
          controls
          style="width: 100%; height: 100%;"
        ></video>
      </div>
      `
    );

  // 点击marker时，打开弹窗
  maker.on("click", () => {
    videoPopup.openOn(map);
  });
});
</script>

<style scoped>
#map {
  height: 100vh;
}
</style>
