<template>
  <div>
    <svg id="svg-canvas" width="100%" height="100%"></svg>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import dagre from "@dagrejs/dagre";

// import dagre from "dagre-d3";
const layoutSize = ref({
  height: 1000,
  width: 1000
});

const nodes = ref([]);
const links = ref([]);
const nodeWidth = 36;
const nodeHeight = 20;

/**
 * 创建一个text，然后获取text的宽度
 */
const gteTextWidth = (text: string, fontSize = 14) => {
  const span = document.createElement("span");
  span.style.visibility = "hidden";
  span.style.fontSize = `${fontSize}px`;
  span.style.fontWeight = "300";
  span.style.fontFamily = "Helvetica Neue, Helvetica, Arial, sans-serf";
  span.style.position = "absolute";
  span.style.top = "-9999px";
  span.style.left = "-9999px";
  span.innerHTML = text;
  document.body.appendChild(span);
  const width = span.offsetWidth;
  document.body.removeChild(span);
  return width;
};

const g = new dagre.graphlib.Graph();
g.setGraph({
  rankdir: "LR"
}).setDefaultEdgeLabel(() => ({}));

g.setNode("kspacey", { label: "Kevin Spacey", width: 144, height: 100 });
g.setNode("swilliams", { label: "Saul Williams", width: 160, height: 100 });
g.setNode("bpitt", { label: "Brad Pitt", width: 108, height: 100 });
g.setNode("hford", { label: "Harrison Ford", width: 168, height: 100 });
g.setNode("lwilson", { label: "Luke Wilson", width: 144, height: 100 });
g.setNode("kbacon", { label: "Kevin Bacon", width: 121, height: 100 });

// Add edges to the graph.
g.setEdge("kspacey", "swilliams");
g.setEdge("swilliams", "kbacon");
g.setEdge("bpitt", "kbacon");
g.setEdge("hford", "lwilson");
g.setEdge("lwilson", "kbacon");

// data.forEach((ele) => {
//   g.setNode(ele.id, {
//     label: ele.name,
//     width: gteTextWidth(ele.name),
//     height: nodeHeight
//   });
//   ele.parentIds.forEach((parentId) => {
//     g.setEdge(parentId, ele.id, {
//       // curve: d3.curveBumpX
//     });
//   });
// });

onMounted(() => {
  dagre.layout(g);
  console.log("🚀 ~ g", g.edges());

  // g.nodes().forEach(function (v) {
  //   const node = g.node(v);
  //   console.log("🚀 ~ node:", node);
  //   node.rx = node.ry = 5;
  // });
  // g.edges().forEach(function (v) {
  //   const edge = g.edge(v);
  //   edge.bindInfo = v;
  //   console.log("🚀 ~ edge:", edge);
  //   // console.log("🚀 ~ edge:", edge);
  // });
  // console.log(g.graph());
  // const render = new dagreD3.render();
  // const svg = d3.select("#svg-canvas"),
  //   svgGroup = svg.append("g");
  // // Run the renderer. This is what draws the final graph.
  // render(svgGroup, g);
  // console.log("g.graph()", g.graph());
  // // Center the graph
  // const xCenterOffset = (svg.attr("width") - g.graph().width) / 2;
  // svgGroup.attr("transform", "translate(" + xCenterOffset + ", 20)");
  // svg.attr("height", g.graph().height + 40);
});
</script>

<style>
#svg-canvas {
  background-color: #fff !important;
}
.clusters rect {
  fill: #00ffd0;
  stroke: #999;
  stroke-width: 1.5px;
}

text {
  font-weight: 300;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serf;
  font-size: 14px;
}

.node rect {
  stroke: #999;
  fill: #fff;
  stroke-width: 1.5px;
}

.edgePath path {
  stroke: #333;
  stroke-width: 1.5px;
}
</style>
