<template>
  <Item title="数据">
    <n-button size="small" text type="primary" @click="showMedaDataModal">编辑MetaData</n-button>
  </Item>
  <MetaDataModal ref="metaDataModalRef" @update:value="updateMetaData"></MetaDataModal>
</template>

<script setup lang="ts">
import { ref } from "vue";

import MetaDataModal from "@/components/SvgEditor/Modal/Attribute/MetaDataConfig/index.vue";

const emit = defineEmits<{
  "update:metaData": [value: Record<string, any>];
}>();

const props = defineProps<{
  data: Record<string, string>;
}>();

const metaDataModalRef = ref<InstanceType<typeof MetaDataModal> | null>(null);
const showMedaDataModal = () => {
  metaDataModalRef.value?.show(props.data);
};

const updateMetaData = (data: Record<string, any>) => {
  const metaData = data.reduce(
    (acc: Record<string, string>, cur: { key: string; value: string }) => {
      acc[cur.key] = cur.value;
      return acc;
    },
    {}
  );
  console.log("🚀 ~ updateMetaData ~ metaData:", metaData);
  emit("update:metaData", metaData);
};
</script>

<style></style>
