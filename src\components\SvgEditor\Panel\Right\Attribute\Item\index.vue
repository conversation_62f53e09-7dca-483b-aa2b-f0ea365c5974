<template>
  <n-grid :cols="24" class="mb-2">
    <n-grid-item :span="titleSpan" v-show="isShowTitle" class="flex justify-end items-center">
      <n-tooltip trigger="hover">
        <template #trigger>
          <span v-show="title">{{ title }}：</span>
        </template>
        {{ tooltip || title }}
      </n-tooltip>
    </n-grid-item>
    <n-grid-item
      :span="isShowTitle ? 24 - titleSpan : 24"
      :offset="isShowTitle ? 0 : 2.5"
      class="flex items-center"
    >
      <slot></slot>
    </n-grid-item>
  </n-grid>
</template>

<script setup lang="ts">
defineProps({
  titleSpan: {
    type: Number,
    default: 6
  },
  isShowTitle: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: ""
  },
  tooltip: {
    type: String,
    default: ""
  }
});
</script>
