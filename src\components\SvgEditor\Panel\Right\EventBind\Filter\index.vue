<template>
  <div>
    <Item title="指标应用">
      <n-select
        v-model:value="id"
        label-field="name"
        value-field="id"
        filterable
        placeholder="请选择指标应用"
        :options="dataBindStore.dataExtractList"
        @update:value="onDataExtractChange"
      />
    </Item>
    <!-- <Item title="指标实例">
      <n-select
        v-model:value="detailId"
        filterable
        placeholder="请选择指标实例"
        :options="dataBindStore.dataExtractDetailOptions"
      />
    </Item> -->
    <FilterForm></FilterForm>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";

import Item from "@/components/SvgEditor/Panel/Right/Attribute/Item/index.vue";
import { useDataBindStore } from "@/stores/";

import FilterForm from "./FilterForm.vue";

const dataBindStore = useDataBindStore();

const id = ref<number | null>(null);

onMounted(() => {
  dataBindStore.getDataExtractList();
});

const onDataExtractChange = async (value: number) => {
  await dataBindStore.getExtractDetail(value);
  await dataBindStore.getDataExtractInfo(value);
};
</script>
<style scoped></style>
