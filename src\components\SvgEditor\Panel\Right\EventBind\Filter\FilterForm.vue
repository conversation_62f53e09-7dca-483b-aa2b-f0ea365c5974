<template>
  <div>
    <div class="flex justify-end">
      <n-button class="mt-1" type="primary" size="small" secondary @click="addMapEventData()"
        >新增</n-button
      >
    </div>
    <n-divider />
    <n-space vertical class="mr--0.75">
      <n-scrollbar style="height: calc(100vh - 300px)">
        <div
          v-for="(eventFilterData, eventFilterIndex) in eventFilterForm"
          :key="eventFilterIndex"
          class="mr-2.5"
        >
          <div class="mb-3 flex justify-between">
            <n-tag :type="eventFilterData.id ? 'success' : 'warning'">
              ID: {{ eventFilterData.id || "未保存" }}
              <template #icon>
                <n-icon
                  :component="SaveEdit20Regular"
                  color="#f0a020"
                  v-if="eventFilterData.status"
                />
                <n-icon :component="CheckmarkCircle12Regular" v-else />
              </template>
            </n-tag>
            <!-- <n-tag type="warning" v-if="eventFilterData.status">
                {{ eventFilterData.status ? "未保存" : "" }}
              </n-tag> -->

            <div>
              <n-tooltip trigger="hover">
                <template #trigger>
                  <n-button
                    class="mx-2"
                    strong
                    secondary
                    :type="eventFilterData.id ? 'primary' : 'warning'"
                    size="small"
                    @click="saveMapEventData(eventFilterIndex)"
                  >
                    <n-icon :component="Save" />
                  </n-button>
                </template>
                保存当前数据
              </n-tooltip>
              <n-tooltip trigger="hover">
                <template #trigger>
                  <n-button
                    strong
                    secondary
                    :type="eventFilterData.id ? 'primary' : 'warning'"
                    size="small"
                    @click="deleteEventFilterData(eventFilterIndex)"
                  >
                    <n-icon :component="Delete" color="#FF9999" />
                  </n-button>
                </template>
                删除当前数据
              </n-tooltip>
            </div>
          </div>

          <div class="flex items-center justify-between mb-3">
            <div class="whitespace-nowrap w-9.4/12">
              <span>名称：</span>
              <n-input
                v-model:value="eventFilterData.name"
                type="text"
                size="small"
                placeholder="输入名称"
                @change="eventFilterData.status = true"
              />
            </div>
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button
                  text
                  type="info"
                  class="mx-1"
                  @click="addTriggerItem(eventFilterData.trigger!, eventFilterData)"
                >
                  <n-icon :component="Add" />
                </n-button>
              </template>
              新增子项
            </n-tooltip>
          </div>

          <n-collapse :trigger-areas="['arrow']">
            <n-collapse-item
              title="图元"
              v-for="(eventData, dataIndex) in eventFilterData.trigger"
              :key="`data-${dataIndex}`"
            >
              <template #header>
                <div style="display: flex; align-items: center; white-space: nowrap; width: 100%">
                  <n-select
                    v-model:value="eventData.objType"
                    filterable
                    size="small"
                    :options="options"
                    placeholder="请选择图元"
                    @update:value="eventFilterData.status = true"
                  />
                </div>
              </template>
              <template #header-extra>
                <n-tooltip trigger="hover">
                  <template #trigger>
                    <n-button
                      text
                      type="error"
                      class="mx-1"
                      @click="
                        deleteTriggerItem(eventFilterData.trigger!, dataIndex, eventFilterData)
                      "
                    >
                      <n-icon :component="Delete" />
                    </n-button>
                  </template>
                  删除当前子项
                </n-tooltip>
              </template>

              <Item title="Key">
                <n-select
                  v-model:value="eventData.domId"
                  filterable
                  size="small"
                  :options="dataBindStore.dataExtractKeyOptions"
                  placeholder="请选择"
                  class="w-50"
                  @update:value="eventFilterData.status = true"
                />
              </Item>
              <Item title="实例">
                <n-select
                  v-model:value="eventData.detailId"
                  filterable
                  size="small"
                  placeholder="请选择指标实例"
                  :options="dataBindStore.dataExtractDetailOptions"
                  class="w-50"
                  @update:value="eventFilterData.status = true"
                />
              </Item>

              <n-collapse :default-expanded-names="['1']" :trigger-areas="['main', 'arrow']">
                <n-collapse-item title="条件配置：" name="1">
                  <template #header-extra>
                    <n-tooltip trigger="hover">
                      <template #trigger>
                        <n-button
                          text
                          style="font-size: 18px"
                          type="info"
                          class="mr-3"
                          @click="addCustomData(eventData, eventFilterData, dataIndex)"
                        >
                          <n-icon :component="Add" />
                        </n-button>
                      </template>
                      新增子条目
                    </n-tooltip>
                  </template>
                  <!-- <div class="flex justify-between pl-2">
                        <div class="my-2">条件配置：</div>
                        <n-tooltip trigger="hover">
                          <template #trigger>
                            <n-button
                              text
                              style="font-size: 18px"
                              type="info"
                              class="mr-3"
                              @click="addCustomData(eventData, dataIndex)"
                            >
                              <n-icon :component="Add" />
                            </n-button>
                          </template>
                          新增子条目
                        </n-tooltip>
                      </div> -->
                  <n-scrollbar style="max-height: 200px" class="pl-2">
                    <n-card
                      v-for="(customItem, customIndex) in eventData.conditions"
                      :key="customIndex"
                      class="flex flex-col mb-2"
                      size="small"
                      content-style="padding: 5px;"
                    >
                      <div class="flex">
                        <n-input
                          v-model:value="customItem.name"
                          type="text"
                          size="small"
                          placeholder="输入条件名称"
                          @change="eventFilterData.status = true"
                        />

                        <n-tooltip trigger="hover">
                          <template #trigger>
                            <n-button
                              class="mx-1"
                              text
                              style="font-size: 18px"
                              @click="
                                deleteCustomData(eventData.conditions, customIndex, eventFilterData)
                              "
                            >
                              <n-icon :component="Subtract" />
                            </n-button>
                          </template>
                          删除子条目
                        </n-tooltip>
                      </div>
                      <div class="flex mt-2">
                        <n-select
                          v-model:value="customItem.key"
                          :options="dataBindStore.dataExtractKeyOptions"
                          filterable
                          size="small"
                          class="mr-1"
                          placeholder="请选择key"
                          @update:value="eventFilterData.status = true"
                        >
                        </n-select>
                        <n-input
                          v-model:value="customItem.value"
                          clearable
                          placeholder="请输入内容"
                          size="small"
                          @change="eventFilterData.status = true"
                        />
                      </div>
                    </n-card>
                  </n-scrollbar>
                </n-collapse-item>
              </n-collapse>
            </n-collapse-item>
          </n-collapse>
          <n-divider />
        </div>
      </n-scrollbar>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { useDialog } from "naive-ui";

import Item from "@/components/SvgEditor/Panel/Right/Attribute/Item/index.vue";
import { useDataBindStore, useMapEventStore, useMapStore } from "@/stores";
import type { IFilterTriggerItem, IMapEventModel, IMapObjItem, ITriggerObject } from "@/types";
import {
  Add,
  CheckmarkCircle12Regular,
  Delete,
  Save,
  SaveEdit20Regular,
  Subtract
} from "@/utils/components/icons";
import { addMapEvent, deleteMapEvent, updateMapEvent } from "@/utils/http/apis";

const dataBindStore = useDataBindStore();
const eventStore = useMapEventStore();
const mapStore = useMapStore();

const dialog = useDialog();

const mapObjData = ref<IMapObjItem[]>([]);

const eventFilterForm = ref<IMapEventModel[]>([]);

const initData = async () => {
  const mapId = mapStore.mapInfo!.mapId;
  mapObjData.value = await eventStore.getObjDataList(mapId);
};

watch(
  () => eventStore.mapEventList,
  (val) => {
    eventFilterForm.value = val || [];
  }
);

const options = computed(() => {
  return mapObjData.value.map((item) => ({
    label: item.objName,
    value: item.objType
  }));
});

const addMapEventData = (index?: number) => {
  const mapId = mapStore.mapInfo!.mapId;

  const newItem = {
    eventType: "filter",
    mapId,
    name: "",
    trigger: [
      {
        domId: "",
        detailId: "",
        objType: "",
        conditions: []
      }
    ],
    status: true
  };
  if (index !== undefined) {
    eventFilterForm.value.splice(index + 1, 0, newItem);
  } else {
    eventFilterForm.value.push(newItem);
  }
};

const saveMapEventData = async (index: number) => {
  const item = eventFilterForm.value[index];
  // console.log("🚀 ~ saveMapEventData ~ item:", item);
  if (!item.name) {
    window.$message.warning("请输入名称");
    return;
  }

  if (item.id) {
    await updateMapEvent(item);
    item.status = false;
    window.$message.success("保存成功");
  } else {
    const id = await addMapEvent(item);
    item.id = id;
    window.$message.success("新增成功");
  }
};

const deleteEventFilterData = (index: number) => {
  dialog.warning({
    title: "警告",
    content: "确定删除当前数据吗？",
    positiveText: "确定",
    negativeText: "取消",
    maskClosable: false,
    closeOnEsc: false,
    onPositiveClick: async () => {
      const item = eventFilterForm.value[index];
      if (item.id) {
        await deleteMapEvent(item.id);
      }
      eventFilterForm.value.splice(index, 1);
      window.$message.success("删除成功");
    },
    onAfterLeave: () => {}
  });
};

const addTriggerItem = (
  triggerData: IFilterTriggerItem[],
  eventFilterData: IMapEventModel,
  dataIndex?: number
) => {
  triggerData.push({
    domId: "",
    detailId: "",
    objType: "",
    conditions: []
  });
  eventFilterData.status = true;
};

const deleteTriggerItem = (
  triggerData: IFilterTriggerItem[],
  dataIndex: number,
  eventFilterData: IMapEventModel
) => {
  triggerData.splice(dataIndex, 1);
  eventFilterData.status = true;
};

const addCustomData = (
  trigger: IFilterTriggerItem,
  eventFilterData: IMapEventModel,
  dataIndex?: number
) => {
  if (!trigger.conditions) {
    trigger.conditions = [];
  }
  trigger.conditions.push({
    name: null,
    key: null,
    value: null
  });
  eventFilterData.status = true;
};

const deleteCustomData = (
  customData: ITriggerObject[],
  dataIndex: number,
  eventFilterData: IMapEventModel
) => {
  customData.splice(dataIndex, 1);
  eventFilterData.status = true;
};

onMounted(() => {
  initData();
});
</script>
