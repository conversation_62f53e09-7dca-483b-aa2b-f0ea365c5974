<template>
  <BaseItem title="线宽">
    <n-input-number size="small" :min="0" placeholder="请输入宽度" @update:value="updateLinkWidth">
    </n-input-number>
  </BaseItem>
  <BaseItem title="线型">
    <n-select
      :options="LinkTypeOptions"
      placeholder="请选择线型"
      @update:value="updateDashedTypeLink"
    />
  </BaseItem>
  <StrokeDasharray @update:value="updateLinkStyle('stroke-dasharray', $event)"></StrokeDasharray>

  <BaseItem title="颜色">
    <n-color-picker
      :show-preview="true"
      :modes="['hex', 'rgb']"
      size="small"
      @update:value="updateLinkStyle('stroke', $event)"
      @complete="updateLinkAttribute"
    />
  </BaseItem>

  <FillColor @update:value="updateLinkStyle" @complete="updateLinkAttribute"></FillColor>
</template>

<script setup lang="ts">
import BaseItem from "@/components/SvgEditor/Panel/Right/Attribute/Item/index.vue";
import FillColor from "@/components/SvgEditor/Panel/Right/Attribute/Item/Common/FillColor.vue";
import StrokeDasharray from "@/components/SvgEditor/Panel/Right/Attribute/Item/Link/StrokeDasharray.vue";
import { useDataStore } from "@/stores";
import { LinkTypeOptions } from "@/utils/constant";
import { updateLink } from "@/utils/http/apis";
const dataStore = useDataStore();

// 更新线宽
const updateLinkWidth = (value: number) => {
  dataStore.linksSelected.forEach((link) => {
    link.linkWidth = value;
    link.style["stroke-width"] = value;
  });
  updateLinkAttribute();
};

// 更新线型
const updateDashedTypeLink = (value: string) => {
  dataStore.linksSelected.forEach((link) => {
    link.dashedLink = value;
    link.style["stroke-dasharray"] = value === "solid" ? "none" : "10,5";
  });
  updateLinkAttribute();
};

// 更新虚线
const updateDashedLink = (value: string) => {
  dataStore.linksSelected.forEach((link) => {
    link.style["stroke-dasharray"] = value;
  });
  updateLinkAttribute();
};

const updateLinkStyle = (key: string, value: string | number) => {
  dataStore.linksSelected.forEach((link) => {
    link.style[key] = value;
  });
  updateLinkAttribute();
};

const updateLinkAttribute = () => {
  updateLink(dataStore.linksSelected);
};
</script>

<style scoped></style>
