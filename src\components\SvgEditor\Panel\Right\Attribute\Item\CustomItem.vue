<template>
  <Item title="自定义">
    <n-select
      :value="type || null"
      filterable
      clearable
      placeholder="选择类型"
      size="small"
      :options="TypeOptions"
      @update:value="onSelectChange"
    />
  </Item>
</template>

<script setup lang="ts">
const emit = defineEmits<{
  "update:value": [value: string];
}>();

defineProps<{
  type?: string;
}>();

const TypeOptions = [
  {
    label: "自定义类型",
    value: "custom"
  }
];
const onSelectChange = (value: string) => {
  emit("update:value", value);
};
</script>

<style></style>
