<template>
  <n-button
    strong
    secondary
    type="primary"
    style="float: right; margin-bottom: 10px"
    size="small"
    @click="showEditScriptModal(newScriptData)"
    >新增</n-button
  >
  <n-data-table :columns="columns" :data="scriptTableData" />

  <EditScript ref="editScriptModalRef" @getScriptData="getScriptData" />
</template>

<script setup lang="ts">
import { h, onMounted, reactive, ref } from "vue";
import { NButton, NIcon, NSwitch, useDialog } from "naive-ui";

import EditScript from "@/components/SvgEditor/Modal/Script/EditScript.vue";
import { useMapStore, useScriptStore } from "@/stores";
import type { IScriptSource } from "@/types";
import { Delete, Edit } from "@/utils/components/icons";
import { deleteScript, updateScript } from "@/utils/http/apis";

const mapStore = useMapStore();
const scriptStore = useScriptStore();
const dialog = useDialog();

const editScriptModalRef = ref<InstanceType<typeof EditScript> | null>(null);

const columns = [
  {
    title: "名称",
    key: "scriptName"
  },
  {
    title: "是否应用",
    key: "status",
    width: 80,
    render(row: IScriptSource) {
      return h(NSwitch, {
        size: "small",
        value: row.status,
        onUpdateValue: (newValue: boolean) => {
          handleStatusChange(row, newValue);
        }
      });
    }
  },
  {
    title: "操作",
    key: "actions",
    width: 75,
    render(row: IScriptSource) {
      return [
        h(
          NButton,
          {
            text: true,
            type: "primary",
            title: "编辑",
            style: "font-size: 24px"
          },
          {
            default: () =>
              h(
                NIcon,
                {
                  size: 20,
                  class: "mr-2",
                  onClick: () => {
                    showEditScriptModal(row);
                  }
                },
                { default: () => h(Edit) }
              )
          }
        ),
        h(
          NButton,
          {
            text: true,
            type: "error",
            title: "删除",
            style: "font-size: 24px"
          },
          {
            default: () =>
              h(
                NIcon,
                {
                  size: 20,
                  onClick: () => {
                    deleteScriptData(row);
                  }
                },
                { default: () => h(Delete) }
              )
          }
        )
      ];
    }
  }
];
const scriptTableData = ref<IScriptSource[]>([]);

const newScriptData = reactive<IScriptSource>({
  id: 0,
  scriptName: "",
  mapId: "",
  script: "",
  status: false
});

onMounted(() => {
  getScriptData();
});

const getScriptData = async () => {
  const mapId = mapStore.mapInfo?.mapId;
  // console.log("🚀 ~ getScriptData ~ mapId:", mapId)
  if (mapId) {
    newScriptData.mapId = mapId;
    scriptTableData.value = await scriptStore.getScriptList(mapId);
  }
};

const handleStatusChange = async (row: IScriptSource, newValue: boolean) => {
  const updateData = row;
  updateData.status = newValue;
  await updateScript(updateData);
  window.$message.success("更改成功");
};

const deleteScriptData = (row: IScriptSource) => {
  dialog.warning({
    title: "警告",
    content: "确定删除当前数据吗？",
    positiveText: "确定",
    negativeText: "取消",
    maskClosable: false,
    closeOnEsc: false,
    onPositiveClick: async () => {
      await deleteScript(row.id);
      window.$message.success("删除成功");
      scriptTableData.value = await scriptStore.getScriptList(row.mapId);
    },
    onAfterLeave: () => {}
  });
};

const showEditScriptModal = (val?: IScriptSource) => {
  editScriptModalRef.value?.show(val);
};
</script>

<style scoped></style>
