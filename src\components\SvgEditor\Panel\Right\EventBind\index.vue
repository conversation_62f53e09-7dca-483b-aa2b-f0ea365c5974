<template>
  <n-spin :show="isLoading">
    <!-- <n-tabs type="card" size="small" animated>
      <n-tab-pane name="filter" tab="筛选" display-directive="show">
        <EventFilter></EventFilter
      ></n-tab-pane>
      <n-tab-pane name="filter1" tab="互动" display-directive="show">
        <Interaction></Interaction
      ></n-tab-pane>
    </n-tabs> -->
    <MouseEvent v-if="isNodeLinkSeleted" />
    <EventFilter v-else></EventFilter>
  </n-spin>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";

import { useDataStore, useMapEventStore, useMapStore } from "@/stores";

import EventFilter from "./Filter/index.vue";
import MouseEvent from "./MouseEvent/index.vue";

const mapStore = useMapStore();
const eventStore = useMapEventStore();
const dataStore = useDataStore();

const isLoading = ref(false);

const initData = async () => {
  isLoading.value = true;
  const mapId = mapStore.mapInfo!.mapId;
  await eventStore.getMapEventList(mapId);
  isLoading.value = false;
};

initData();

const isNodeLinkSeleted = computed(() => {
  return dataStore.nodesSelected.length || dataStore.linksSelected.length;
});
</script>

<style></style>
