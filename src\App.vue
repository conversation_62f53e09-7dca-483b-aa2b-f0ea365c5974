<template>
  <n-config-provider :theme="darkTheme" :locale="zhCN" :date-locale="dateZhCN">
    <n-dialog-provider>
      <n-modal-provider>
        <n-message-provider>
          <n-notification-provider>
            <n-spin :show="commonStore.isLoading" class="relative h-100vh">
              <SideDrawer></SideDrawer>
              <BaseView></BaseView>
            </n-spin>
          </n-notification-provider>
        </n-message-provider>
      </n-modal-provider>
    </n-dialog-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { darkTheme } from "naive-ui";
import { dateZhCN, zhCN } from "naive-ui";

import BaseView from "@/components/Common/BaseView/index.vue";
import SideDrawer from "@/components/Common/SideDrawer/index.vue";

import { useCommonStore } from "./stores";

const commonStore = useCommonStore();
</script>
