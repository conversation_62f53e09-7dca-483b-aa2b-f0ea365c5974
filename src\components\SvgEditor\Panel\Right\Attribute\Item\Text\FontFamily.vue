<template>
  <BaseItem title="字体选择">
    <n-select
      style="width: 100%"
      :value="value"
      size="small"
      clearable
      placeholder="请选择字体"
      :options="fontOptions"
      @update:value="onChange"
    />
  </BaseItem>
</template>

<script setup lang="ts">
import BaseItem from "../index.vue";

const emit = defineEmits<{
  "update:value": [value: string];
}>();

defineProps<{
  value?: string | number | null;
}>();

const fontOptions = [
  { label: "Digital-7", value: "Digital-7" } // Digital font option
];

const onChange = (value: string) => {
  emit("update:value", value);
};
</script>

<style></style>
