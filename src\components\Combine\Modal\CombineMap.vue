<template>
  <n-modal
    v-model:show="isVisible"
    preset="dialog"
    title="配置"
    size="huge"
    :bordered="false"
    :show-icon="false"
    :close-on-esc="false"
    :maskClosable="false"
    positive-text="确认"
    negative-text="取消"
    @positive-click="submit"
    @negative-click="hide"
    style="margin-top: 20vh"
  >
    <n-form
      ref="mapFormRef"
      :model="mapModel"
      :rules="mapRules"
      label-placement="top"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-grid :cols="24" :x-gap="15">
        <n-form-item-gi :span="24" label="名称" path="mapName">
          <n-input v-model:value="mapModel.mapName" placeholder="文件名称" />
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="目录" path="menuId">
          <n-cascader
            v-model:value="mapModel.menuId"
            placeholder="所属目录"
            expand-trigger="hover"
            :options="menuStore.menuCascaderList"
            check-strategy="all"
            filterable
          />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { FormInst } from "naive-ui";

import { useMenuStore } from "@/stores";

const emit = defineEmits<{
  onValueUpdate: [
    {
      mapName: string;
      menuId: string;
    }
  ];
}>();
const menuStore = useMenuStore();
const mapFormRef = ref<FormInst | null>(null);

const isVisible = ref(false);
const mapModel = ref({
  mapName: "",
  menuId: "0"
});

const mapRules = {
  mapName: [{ required: true, message: "请输入名称", trigger: "blur" }]
};

const show = () => {
  isVisible.value = true;
};

const hide = () => {
  isVisible.value = false;
};

const submit = () => {
  mapFormRef.value?.validate((errors) => {
    if (errors) return;
    if (mapModel.value.menuId === "0") {
      window.$message.error("不能选择根目录！");
      return;
    }

    hide();
    emit("onValueUpdate", mapModel.value);
  });
  return false;
};

defineExpose({
  show,
  hide
});
</script>
