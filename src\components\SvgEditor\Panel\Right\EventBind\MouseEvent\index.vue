<template>
  <div>
    <div class="flex justify-end">
      <n-button text style="font-size: 18px" type="info" class="mx-1" @click="addMouseEventData()">
        <n-icon :component="Add" />
      </n-button>

      <n-button
        text
        style="font-size: 18px"
        type="primary"
        class="mx-1"
        @click="saveMouseEventData"
      >
        <n-icon :component="Save" />
      </n-button>
    </div>
    <n-divider />
    <n-scrollbar style="height: calc(100vh - 300px)" v-if="mouseEventForm.length">
      <div
        v-for="(mouseEventItem, mouseEventIndex) in mouseEventForm"
        :key="mouseEventIndex"
        class="mr-2.5"
      >
        <!-- <div class="mb-3 flex justify-end">

        </div> -->
        <!-- <Item title="名称">
          <n-input
            v-model:value="mouseEventItem.eventName"
            type="text"
            size="small"
            placeholder="请输入事件名称"
          />

          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button
                strong
                secondary
                type="error"
                size="small"
                class="ml-2"
                @click="deleteMouseEventItem(mouseEventIndex)"
              >
                <n-icon :component="Delete" />
              </n-button>
            </template>
            删除当前数据
          </n-tooltip>
        </Item> -->
        <div class="flex justify-end mb-1">
          <n-button
            strong
            secondary
            type="error"
            size="small"
            class="ml-2"
            @click="deleteMouseEventItem(mouseEventIndex)"
          >
            <n-icon :component="Delete" />
          </n-button>
        </div>
        <BaseItem title="方式">
          <n-select
            v-model:value="mouseEventItem.eventType"
            filterable
            size="small"
            :options="EventOptions"
            placeholder="触发方式"
          />
        </BaseItem>
        <BaseItem title="ID">
          <n-input
            v-model:value="mouseEventItem.resourceId"
            type="text"
            size="small"
            placeholder="请输入资源名称"
          />
        </BaseItem>
        <BaseItem title="Code">
          <n-input
            v-model:value="mouseEventItem.resourceCode"
            type="text"
            size="small"
            placeholder="请输入资源Code"
          />
        </BaseItem>
        <BaseItem title="宽度">
          <n-input-number
            v-model:value="mouseEventItem.width"
            size="small"
            placeholder="请输入宽度"
          />
        </BaseItem>
        <BaseItem title="高度">
          <n-input-number
            v-model:value="mouseEventItem.height"
            size="small"
            placeholder="请输入高度"
          />
        </BaseItem>
        <BaseItem title="分组">
          <n-input
            v-model:value="mouseEventItem.groupName"
            type="text"
            size="small"
            placeholder="分组"
          />
        </BaseItem>
        <BaseItem title="全局变量">
          <n-input
            v-model:value="mouseEventItem.name"
            type="text"
            size="small"
            placeholder="全局变量"
          />
        </BaseItem>
        <BaseItem title="变量值">
          <n-input
            v-model:value="mouseEventItem.value"
            type="text"
            size="small"
            placeholder="全局变量值"
          />
        </BaseItem>
        <n-divider />
      </div>
    </n-scrollbar>
    <n-empty v-else description="暂无数据"> </n-empty>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, toRaw, watch } from "vue";
import { useDialog } from "naive-ui";

import BaseItem from "@/components/SvgEditor/Panel/Right/Attribute/Item/index.vue";
import { useDataStore } from "@/stores";
import type { IMouseEvent } from "@/types";
import { Add, Delete, Save } from "@/utils/components/icons";
import { EventOptions } from "@/utils/constant";
import { updateNode, updateNodesLinks } from "@/utils/http/apis";
// import { ComparisonOptions } from "@/utils/constant";
const dataStore = useDataStore();

const dialog = useDialog();

const mouseEventForm = ref<IMouseEvent[]>([]);

onMounted(() => {
  watch(
    () => [dataStore.currentNode, dataStore.currentLink],
    ([node, link]) => {
      const { mouseEvent } = node || link || {};
      mouseEventForm.value = mouseEvent ? window.structuredClone(toRaw(mouseEvent)) : [];
    },
    { immediate: true }
  );
});
const addMouseEventData = (index?: number) => {
  const newItem = {
    eventType: "click",
    // eventName: "",
    resourceId: "",
    resourceCode: "",
    width: 1000,
    height: 800
  };
  if (index !== undefined) {
    mouseEventForm.value.splice(index + 1, 0, newItem);
  } else {
    mouseEventForm.value.push(newItem);
  }
};

const deleteMouseEventItem = (index: number) => {
  dialog.warning({
    title: "警告",
    content: "确定删除当前数据吗？",
    positiveText: "确定",
    negativeText: "取消",
    maskClosable: false,
    closeOnEsc: false,
    onPositiveClick: async () => {
      mouseEventForm.value.splice(index, 1);
      if (!dataStore.currentNode) return;
      dataStore.currentNode.mouseEvent = mouseEventForm.value;
      updateNode([dataStore.currentNode]);
    },
    onAfterLeave: () => {}
  });
};

const saveMouseEventData = () => {
  // if (!dataStore.currentNode) return;
  // dataStore.currentNode.mouseEvent = mouseEventForm.value;
  // updateNode([dataStore.currentNode]);

  dataStore.nodesSelected.forEach((node) => {
    node.mouseEvent = mouseEventForm.value;
  });
  dataStore.linksSelected.forEach((link) => {
    link.mouseEvent = mouseEventForm.value;
  });
  updateNodesLinks({
    nodes: dataStore.nodesSelected,
    links: dataStore.linksSelected
  });
};
</script>
