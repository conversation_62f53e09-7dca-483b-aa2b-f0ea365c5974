// import dagreD3 from "dagre-d3";
import dagre, { type GraphEdge, type Node } from "@dagrejs/dagre";

import type { IImportNodeGraph } from "@/types";

export interface IGraphNode extends Node {
  raw: IImportNodeGraph;
}
export interface IGraphEdge extends GraphEdge {
  bindInfo?: {
    v: string;
    w: string;
  };
}
const layoutSize = {
  height: 1000,
  width: 1000
};

const nodeWidth = 36;
const nodeHeight = 20;

/**
 * 创建一个text，然后获取text的宽度
 */
export const gteTextWidth = (text: string, fontSize = 14) => {
  const span = document.createElement("span");
  span.style.visibility = "hidden";
  span.style.fontSize = `${fontSize}px`;
  span.style.position = "absolute";
  span.style.top = "-9999px";
  span.style.left = "-9999px";
  span.innerHTML = text;
  document.body.appendChild(span);
  const width = span.clientWidth;
  document.body.removeChild(span);
  return width;
};

/**
 * 创建一个text，然后获取text的高度，换行
 */
export const gteTextHeight = (text: string, fontSize = 14, width = 200) => {
  const span = document.createElement("span");
  span.style.visibility = "hidden";
  span.style.fontSize = `${fontSize}px`;
  span.style.position = "absolute";
  span.style.width = `${width}px`;
  span.style.top = "-9999px";
  span.style.left = "-9999px";
  span.innerHTML = text;
  document.body.appendChild(span);
  const height = span.clientHeight;
  document.body.removeChild(span);
  return height;
};

export const getDagData = (data: IImportNodeGraph[], direction: string) => {
  const g = new dagre.graphlib.Graph();
  const nodes: IGraphNode[] = [];
  const edges: IGraphEdge[] = [];
  g.setGraph({
    rankdir: direction
  }).setDefaultEdgeLabel(() => ({}));

  data.forEach((ele) => {
    g.setNode(ele.id, {
      label: ele.name,
      width: gteTextWidth(ele.name) + 16,
      height: nodeHeight + 6,
      raw: ele
    });
    ele.parentIds.forEach((parentId) => {
      g.setEdge(parentId, ele.id, {
        // curve: d3.curveBumpX
      });
    });
  });

  dagre.layout(g);

  g.nodes().forEach(function (v) {
    const node = g.node(v) as IGraphNode;
    nodes.push(node);
  });

  g.edges().forEach(function (v) {
    const edge = g.edge(v);
    edge.bindInfo = v;
    edges.push(edge);
  });

  const { width, height } = g.graph();

  return {
    dagNodes: nodes,
    dagEdges: edges,
    width,
    height
  };
};
