<template>
  <BaseItem title="填充">
    <n-color-picker
      :default-value="data"
      :show-preview="true"
      :modes="['hex', 'rgb']"
      size="small"
      @update:value="updateStyle"
      @complete="onComplete"
    />
  </BaseItem>
</template>

<script setup lang="ts">
import BaseItem from "@/components/SvgEditor/Panel/Right/Attribute/Item/index.vue";

defineProps<{
  data?: string;
}>();

const emit = defineEmits<{
  "update:value": [key: string, value: string];
  complete: [];
}>();

let color = "";

const updateStyle = (value: string) => {
  // const dataStore = useDataStore();
  // dataStore.linksSelected.forEach((link) => {
  //   link.style.fill = value;
  //   attrUpdateLink(link);
  // });
  emit("update:value", "fill", value);
};

const onComplete = (value: string) => {
  emit("complete");
};
</script>

<style scoped></style>
