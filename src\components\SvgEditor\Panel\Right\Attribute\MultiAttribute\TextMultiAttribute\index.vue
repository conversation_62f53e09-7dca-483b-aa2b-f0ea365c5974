<template>
  <BaseItem title="内容">
    <n-input
      type="textarea"
      size="small"
      placeholder="请输入文字"
      @update:value="updateStringAttr('nodeText', $event)"
    />
  </BaseItem>
  <!-- 字体、大小、颜色、旋转角度 -->
  <BaseItem title="大小">
    <n-input-number
      size="small"
      :min="0"
      placeholder="请输入文字大小"
      @update:value="updateStyles('font-size', $event)"
    />
  </BaseItem>
  <FontFamily @update:value="updateTextStyles('font-family', $event)" />
  <FontWeight @update:value="updateTextStyles('font-weight', $event)" />
  <LineHeight @update:value="updateTextStyles('line-height', $event)" />
  <BaseItem title="颜色">
    <n-color-picker
      size="small"
      :modes="['rgb', 'hex']"
      @update:value="updateStyles('fontColor', $event)"
    />
  </BaseItem>
  <BaseItem title="位置">
    <n-flex :wrap="false">
      <n-input-number
        size="small"
        :min="0"
        :show-button="false"
        @update:value="updatePostion('x', $event)"
      />
      <n-input-number
        size="small"
        :min="0"
        :show-button="false"
        @update:value="updatePostion('y', $event)"
      />
    </n-flex>
  </BaseItem>
  <TextAlign @update:value="alignText" />
</template>

<script setup lang="ts">
import { useDataStore } from "@/stores";
import type { ITextAlign } from "@/types";
import { TextAlignMap } from "@/utils/constant";
import { updateNode } from "@/utils/http/apis";

import BaseItem from "../../Item/index.vue";
import FontFamily from "../../Item/Text/FontFamily.vue";
import FontWeight from "../../Item/Text/FontWeight.vue";
import LineHeight from "../../Item/Text/LineHeight.vue";
import TextAlign from "../../Item/Text/TextAlign.vue";

const dataStore = useDataStore();

const updateNodeAttribute = () => {
  updateNode(dataStore.nodesSelected);
};

const updateStringAttr = (type: "compClass" | "nodeText", value: string) => {
  dataStore.nodesSelected.forEach((node) => {
    node[type] = value;
  });
  updateNodeAttribute();
};

const updateStyles = (key: string, value: string) => {
  dataStore.nodesSelected.forEach((node) => {
    if (key === "fontColor") {
      node.fontColor = value;
    } else if (key === "font-size") {
      node.fontSize = value;
      node.style["font-size"] = value + "px";
    } else {
      node.style[key] = value;
    }
    node.nodeStyles = JSON.stringify(node.style);
  });

  updateNodeAttribute();
};

const updateTextStyles = (key: string, value: string) => {
  dataStore.nodesSelected.forEach((node) => {
    node.textStyle[key] = value;
    node.textStyles = JSON.stringify(node.textStyle);
  });

  updateNodeAttribute();
};

const alignText = (align: ITextAlign) => {
  dataStore.nodesSelected.forEach((node) => {
    node.textStyle["text-align"] = TextAlignMap[align];
    node.textStyles = JSON.stringify(node.textStyle);
  });
  updateNodeAttribute();
};

const updatePostion = (key: "x" | "y", value: number) => {
  dataStore.nodesSelected.forEach((node) => {
    node[key] = value;
  });
  updateNodeAttribute();
};
</script>

<style scoped></style>
