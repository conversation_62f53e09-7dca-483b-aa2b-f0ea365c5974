<template>
  <div>
    <div class="flex justify-end">
      <n-button class="mt-1" type="primary" size="small" secondary @click="addMapEventData()">
        新增</n-button
      >
    </div>
    <n-divider />
    <n-scrollbar style="height: calc(100vh - 300px)" v-if="interactionForm.length">
      <div
        v-for="(interactionItem, interactionIndex) in interactionForm"
        :key="interactionIndex"
        class="mr-2.5"
      >
        <div class="mb-3 flex justify-between">
          <n-tag :type="interactionItem.id ? 'success' : 'warning'">
            ID: {{ interactionItem.id || "未保存" }}
          </n-tag>

          <div>
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button
                  class="mx-2"
                  strong
                  secondary
                  type="info"
                  size="small"
                  @click="saveInteractionItem(interactionIndex)"
                >
                  <n-icon :component="Save" />
                </n-button>
              </template>
              保存当前数据
            </n-tooltip>
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button
                  strong
                  secondary
                  type="error"
                  size="small"
                  @click="deleteInteractionItem(interactionIndex)"
                >
                  <n-icon :component="Delete" />
                </n-button>
              </template>
              删除当前数据
            </n-tooltip>
          </div>
        </div>
        <Item title="名称" :title-span="4">
          <n-input
            v-model:value="interactionItem.name"
            type="text"
            size="small"
            placeholder="请输入名称"
          />
        </Item>
        <Item title="方式" :title-span="4">
          <n-select
            v-model:value="interactionItem.trigger[0].action"
            filterable
            size="small"
            :options="EventOptions"
            placeholder="触发方式"
            class="mr-2"
          />
        </Item>
        <Item title="阈值" :title-span="4">
          <n-input-number
            v-model:value="interactionItem.trigger[0].threshold"
            size="small"
            placeholder="阈值"
            clearable
          />
        </Item>
        <!-- <div class="flex">
              <n-select
                v-model:value="interactionItem.trigger.action"
                filterable
                size="small"
                :options="EventOptions"
                placeholder="事件名称"
                class="mr-2"
              />
              <n-input-number
                v-model:value="interactionItem.trigger.threshold"
                size="small"
                placeholder="阈值"
                clearable
              />
            </div> -->

        <div class="flex justify-between mb-2 mt-2">
          <span>命令列表：</span>
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button
                text
                size="small"
                type="primary"
                @click="showCommandListModal(interactionIndex)"
              >
                <n-icon :component="Settings" />
              </n-button>
            </template>
            设置触发执行命令
          </n-tooltip>
        </div>
        <n-card
          size="small"
          content-style="padding: 10px;"
          v-for="(triggerItem, triggerIndex) in interactionItem.trigger"
          :key="triggerIndex"
        >
          <n-space v-if="triggerItem.commands.length">
            <n-tag v-for="(command, commandIndex) in triggerItem.commands" :key="commandIndex">
              {{ command.cmdName }}
            </n-tag>
          </n-space>
          <n-empty v-else description="暂无数据"> </n-empty>
        </n-card>
        <n-divider />
      </div>
    </n-scrollbar>
    <n-empty v-else description="暂无数据"> </n-empty>
  </div>
  <CommandListModal ref="commandListModalRef" @update:value="onCommandUpdate"></CommandListModal>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useDialog } from "naive-ui";

import Item from "@/components/SvgEditor/Panel/Right/Attribute/Item/index.vue";
import CommandListModal from "@/components/SvgEditor/Panel/Right/Modal/Interaction/CommandList.vue";
import { useMapEventStore, useMapStore } from "@/stores";
import type { ICommand, IInteractionModel } from "@/types";
import { Delete, Save, Settings } from "@/utils/components/icons";
import { EventOptions } from "@/utils/constant";
// import { ComparisonOptions } from "@/utils/constant";
import { addMapEvent, deleteMapEvent, updateMapEvent } from "@/utils/http/apis";
const eventStore = useMapEventStore();
const mapStore = useMapStore();

const dialog = useDialog();

const interactionForm = ref<IInteractionModel[]>([]);
const activeIndex = ref<number | null>(null);
const commandListModalRef = ref<InstanceType<typeof CommandListModal> | null>(null);

watch(
  () => eventStore.interactionList,
  (val) => {
    interactionForm.value = val || [];
  }
);

const addMapEventData = (index?: number) => {
  const mapId = mapStore.mapInfo!.mapId;

  const newItem = {
    eventType: "interaction",
    mapId,
    name: "",
    trigger: [
      {
        action: null,
        threshold: 0,
        commands: []
      }
    ]
  };
  if (index !== undefined) {
    interactionForm.value.splice(index + 1, 0, newItem);
  } else {
    interactionForm.value.push(newItem);
  }
};

const saveInteractionItem = async (index: number) => {
  const item = interactionForm.value[index];
  if (!item.name) {
    window.$message.warning("请输入名称");
    return;
  }

  if (item.id) {
    await updateMapEvent(item);
    window.$message.success("保存成功");
  } else {
    const id = await addMapEvent(item);
    item.id = id;
    window.$message.success("新增成功");
  }
};

const deleteInteractionItem = (index: number) => {
  dialog.warning({
    title: "警告",
    content: "确定删除当前数据吗？",
    positiveText: "确定",
    negativeText: "取消",
    maskClosable: false,
    closeOnEsc: false,
    onPositiveClick: async () => {
      const item = interactionForm.value[index];
      if (item.id) {
        await deleteMapEvent(item.id);
      }
      interactionForm.value.splice(index, 1);
      window.$message.success("删除成功");
    },
    onAfterLeave: () => {}
  });
};

const showCommandListModal = (index: number) => {
  activeIndex.value = index;
  const val = interactionForm.value[activeIndex.value].trigger[0].commands;
  commandListModalRef.value?.show(val);
};

const onCommandUpdate = (commands: ICommand[]) => {
  if (activeIndex.value !== null) {
    interactionForm.value[activeIndex.value].trigger[0].commands = commands;
  }
};
</script>
