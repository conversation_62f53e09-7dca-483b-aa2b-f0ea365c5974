<template>
  <BaseItem title="尺寸">
    <n-input-number
      v-model:value="width"
      class="mr-2"
      size="small"
      :show-button="false"
      placeholder="宽"
    />
    <n-input-number v-model:value="height" size="small" :show-button="false" placeholder="高" />
  </BaseItem>
  <BaseItem title="间距">
    <n-input-number
      v-model:value="gap"
      style="width: 50%"
      size="small"
      :show-button="false"
      placeholder="间距"
    />
  </BaseItem>

  <BaseItem title="">
    <div class="flex">
      <n-upload
        class="w-auto mr-2"
        v-model:file-list="fileList"
        :max="1"
        :default-upload="false"
        :show-file-list="false"
        @change="onUploadChange"
      >
        <n-button secondary type="info" size="small">上传</n-button>
      </n-upload>
      <n-button secondary type="primary" size="small" class="mr-2" @click="drawInnerNodes"
        >刷新</n-button
      >
      <n-button secondary type="primary" size="small" class="mr-2" @click="addInnerNodes"
        >保存</n-button
      >
      <n-button secondary size="small" @click="removeInnerNodes">重置</n-button>
    </div>
  </BaseItem>
  <ImportMoveToSublayer></ImportMoveToSublayer>
</template>

<script setup lang="ts">
import BaseItem from "@/components/SvgEditor/Panel/Right/Attribute/Item/index.vue";
import { useInnerNode } from "@/hooks/map/link/useInnerNode";

const {
  width,
  height,
  gap,
  fileList,
  onUploadChange,
  drawInnerNodes,
  removeInnerNodes,
  addInnerNodes
} = useInnerNode();
</script>

<style scoped></style>
