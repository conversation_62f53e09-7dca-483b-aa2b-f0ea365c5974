<template>
  <g id="linkGroup" class="link-group">
    <TransitionGroup name="fade">
      <Link
        v-for="link in dataStore.links"
        :key="link.linkId"
        :data="link"
        @update-bbox="updateBbox($event, link)"
      ></Link>
    </TransitionGroup>
  </g>
  <g id="nodeGroup" class="node-group">
    <TransitionGroup name="fade">
      <template v-for="node in dataStore.nodes" :key="node.nodeId">
        <Node :data="node"></Node>
      </template>
    </TransitionGroup>
  </g>
  <g id="mergeLinkGroup"></g>
  <g id="mergeNodeGroup"></g>
</template>

<script setup lang="ts">
import { useDataStore } from "@/stores";
import type { ILink } from "@/types";

import Link from "./Dom/Link.vue";
import Node from "./Dom/Node.vue";

const dataStore = useDataStore();

const updateBbox = (bbox: DOMRect, link: ILink) => {
  const { x, y, width, height } = bbox;

  link.x = x;
  link.y = y;
  link.width = width;
  link.height = height;
};
</script>
<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
