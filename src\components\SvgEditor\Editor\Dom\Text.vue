<template>
  <!-- <foreignObject :width="data.width" :height="data.height">
    <div xmlns="http://www.w3.org/1999/xhtml" style="background-color: transparent">
      <div style="display: flex; align-items: center; white-space: pre-wrap" :style="style">
        {{ data.nodeText }}
      </div>
    </div>
  </foreignObject> -->
  <text :width="data.width" :height="data.height" :style="style">
    <tspan>{{ data.nodeText }}</tspan>
  </text>
</template>

<script setup lang="ts">
import { computed } from "vue";

import type { INode } from "@/types";

const props = defineProps<{
  data: INode;
}>();

const style = computed(() => {
  // return {
  //   ...props.data.textStyle,
  //   width: props.data.width + "px",
  //   height: props.data.height + "px",
  //   color: props.data.fontColor,
  //   fontSize: props.data.fontSize + "px"
  // };

  const { textStyle, style } = props.data;
  return {
    ...textStyle,
    display: (style.display || "block") as string,
    justifyContent: (style["justify-content"] || "unset") as string,
    alignItems: (style["align-items"] || "unset") as string,
    width: props.data.width + "px",
    height: props.data.height + "px",
    color: props.data.fontColor,
    fontSize: props.data.fontSize + "px",
    
  };
});
</script>

<style></style>
