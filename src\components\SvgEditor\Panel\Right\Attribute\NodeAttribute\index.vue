<template>
  <Item title="ID">
    <p class="truncate" :title="dataStore.currentNode?.nodeId">
      {{ dataStore.currentNode?.nodeId }}
    </p>
  </Item>
  <Item title="类型">
    <p class="truncate">{{ dataStore.currentNode?.nodeType }}</p>
  </Item>
  <n-divider />
  <n-collapse :default-expanded-names="['base', 'style', 'nodeText']">
    <n-collapse-item title="基础" name="base">
      <Item title="尺寸">
        <n-flex :wrap="false">
          <n-input-number
            :value="dataStore.currentNode?.width"
            size="small"
            :min="0"
            @update:value="updateSize('width', $event)"
          />
          <n-input-number
            :value="dataStore.currentNode?.height"
            size="small"
            :min="0"
            @update:value="updateSize('height', $event)"
          />
        </n-flex>
      </Item>
      <Item title="位置">
        <n-flex :wrap="false">
          <n-input-number
            :value="dataStore.currentNode?.x"
            size="small"
            @update:value="updatePosition('x', $event)"
          />
          <n-input-number
            :value="dataStore.currentNode?.y"
            size="small"
            @update:value="updatePosition('y', $event)"
          />
        </n-flex>
      </Item>
      <Item title="角度">
        <n-input-number
          :value="dataStore.currentNode?.rotate"
          size="small"
          :min="-180"
          :max="180"
          @update:value="updatePosition('rotate', $event)"
        />
      </Item>
      <FlexLayout @update:value="updateLayout" />
    </n-collapse-item>
    <n-collapse-item title="绑定" name="bind">
      <MetaData
        :data="dataStore.currentNode?.metaData || {}"
        @update:metaData="updateMetaData"
      ></MetaData>

      <BindMeta
        :type="dataStore.currentNode?.nodeType"
        @update:bindMeta="changeMetaIcon"
      ></BindMeta>

      <CustomItem
        :type="dataStore.currentNode?.bindData.nodeType"
        @update:value="updateBindData"
      ></CustomItem>
    </n-collapse-item>
    <n-collapse-item title="样式" name="style">
      <Item title="填充">
        <n-color-picker
          :default-value="dataStore.currentNode?.style.fill"
          size="small"
          :modes="['hex', 'rgb']"
          @update:value="updateStyles('fill', $event)"
        />
      </Item>
      <NodeBackground />
    </n-collapse-item>
    <!-- <n-collapse-item title="文字" name="text" v-if="dataStore.currentNode?.nodeType === 'text'">
      <Item title="内容">
        <n-input
          :value="dataStore.currentNode?.nodeText"
          size="small"
          placeholder="请输入文字"
          @update:value="updateStringAttr('nodeText', $event)"
        />
      </Item>
      <Item title="大小">
        <n-input-number
          :default-value="parseFloat(dataStore.currentNode?.fontSize)"
          size="small"
          :min="0"
          placeholder="请输入文字大小"
          @update:value="updateStyles('font-size', $event)"
        />
      </Item>
      <Item title="颜色">
        <n-color-picker
          :default-value="dataStore.currentNode?.style.fill"
          size="small"
          :modes="['rgb', 'hex']"
          @update:value="updateStyles('fill', $event)"
        />
      </Item>
    </n-collapse-item> -->
    <n-collapse-item title="文字" name="nodeText">
      <TextAttr />
    </n-collapse-item>
  </n-collapse>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

import { useDataStore } from "@/stores";
import type { IMetaItem, INodeAlign } from "@/types";
import { updateNode } from "@/utils/http/apis";
import { formatObject } from "@/utils/tools";

import BindMeta from "../Item/BindMeta.vue";
import CustomItem from "../Item/CustomItem.vue";
import Item from "../Item/index.vue";
import MetaData from "../Item/MetaData.vue";
import FlexLayout from "../Item/Node/FlexLayout.vue";

import NodeBackground from "./Background/index.vue";
import TextAttr from "./TextAttr/index.vue";
const dataStore = useDataStore();

const updateNodeAttribute = () => {
  if (!dataStore.currentNode) return;
  updateNode([dataStore.currentNode]);
};

const updateMetaData = (value: Record<string, string>) => {
  if (!dataStore.currentNode) return;
  dataStore.currentNode.metaData = value;
  updateNodeAttribute();
};

const changeMetaIcon = (value: string, row: IMetaItem) => {
  if (!dataStore.currentNode) return;
  dataStore.currentNode.nodeType = value;
  dataStore.currentNode.style.image = row.objImg;
  dataStore.currentNode.nodeStyles = JSON.stringify(dataStore.currentNode.style);

  dataStore.currentNode.svgData = "";
  //   removeNode(dataStore.currentNode.nodeId);
  //   drawNodes();
  updateNode([dataStore.currentNode]);
};

const updateBindData = (value: string) => {
  if (!dataStore.currentNode) return;
  dataStore.currentNode.bindData.nodeType = value;
  updateNodeAttribute();
};

const updateStringAttr = (type: "compClass" | "nodeText", value: string) => {
  if (!dataStore.currentNode) return;
  dataStore.currentNode[type] = value;
  updateNodeAttribute();
};

const updateSize = (key: "width" | "height", value: number) => {
  if (!dataStore.currentNode) return;
  dataStore.currentNode[key] = value;
  updateNodeAttribute();
};

const updatePosition = (key: "x" | "y" | "rotate", value: number) => {
  if (!dataStore.currentNode) return;
  dataStore.currentNode[key] = value;
  updateNodeAttribute();
};

const updateStyles = (key: string, value: string) => {
  if (!dataStore.currentNode) return;
  if (key === "font-size") {
    dataStore.currentNode.fontSize = value;
    dataStore.currentNode.style["font-size"] = value + "px";
  } else {
    dataStore.currentNode.style[key] = value;
  }
  dataStore.currentNode.nodeStyles = JSON.stringify(dataStore.currentNode.style);
  updateNodeAttribute();
};

const updateLayout = (direction: string, value: INodeAlign) => {
  if (!dataStore.currentNode) return;
  dataStore.currentNode.style.display = "flex";
  dataStore.currentNode.style[direction] = value;
  dataStore.currentNode.nodeStyles = JSON.stringify(dataStore.currentNode.style);
  updateNodeAttribute();
};
</script>

<style scoped></style>
