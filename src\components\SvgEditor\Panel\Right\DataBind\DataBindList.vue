<template>
  <div class="flex justify-end">
    <n-button size="small" type="primary" class="mb-5" @click="updateGroupAttribute">保存</n-button>
  </div>
  <n-scrollbar style="max-height: 700px">
    <div v-for="(item, index) in dataBindForms" :key="index">
      <n-divider></n-divider>
      <Item title="ID">
        <div class="flex items-center justify-between pr-2">
          <span @click="highlightEle(item)" class="cursor-pointer">
            {{ data.groupType === 0 ? item.type : item.nodeLinkId }}</span
          >
          <div>
            <n-button text size="small" class="ml-2" @click="showDataBindModal(index)">
              <n-icon style="font-size: 24px">
                <Settings></Settings>
              </n-icon>
            </n-button>
            <n-button
              v-if="data.groupType !== 0"
              text
              size="small"
              class="ml-2"
              @click="showConditionConfig(index)"
            >
              <n-icon style="font-size: 24px">
                <DataObject></DataObject>
              </n-icon>
            </n-button>
            <n-button text type="error" size="small" class="ml-2" @click="deleteDataBind(index)">
              <n-icon style="font-size: 24px">
                <Delete></Delete>
              </n-icon>
            </n-button>
          </div>
        </div>
      </Item>
      <Item title="实例">
        <n-input
          v-model:value="item.detailId"
          placeholder="实例ID"
          size="small"
          disabled
          @update:value="onBindMapDataChange"
        />
      </Item>
      <Item title="DomId" tooltip="DomIdKey">
        <n-input
          v-model:value="item.key"
          placeholder="key"
          size="small"
          disabled
          @update:value="onBindMapDataChange"
        />
      </Item>
    </div>
  </n-scrollbar>

  <ConditionConfigModal
    ref="conditionConfigModalRef"
    @onValueUpdate="onConditionConfigUpdate"
  ></ConditionConfigModal>
  <DataBindModal ref="dataBindModalRef" @onValueUpdate="onDataBindModalUpdate"></DataBindModal>
</template>

<script setup lang="ts">
import { ref, watchEffect } from "vue";
import { NButton, useDialog } from "naive-ui";

import DataBindModal from "@/components/Common/Modal/DataBindConfig/index.vue";
import ConditionConfigModal from "@/components/SvgEditor/Panel/Right/DataBind/Modal/ConditionConfig.vue";
import { useMapStore } from "@/stores";
import type { IGroupData, IGroupDataBind, IObjDataBindInfo } from "@/types";
import { DataObject, Delete, Settings } from "@/utils/components/icons";
import { highlightNodeLink } from "@/utils/editor/draw";
import { updateMapGroupData } from "@/utils/http/apis";
import { getGroupDataList } from "@/utils/tools";

const dialog = useDialog();
const props = defineProps<{
  data: IGroupData;
}>();

const mapStore = useMapStore();

const dataBindForms = ref<IGroupDataBind[]>([]);
const conditionConfigModalRef = ref<InstanceType<typeof ConditionConfigModal> | null>(null);
const dataBindModalRef = ref<InstanceType<typeof DataBindModal> | null>(null);
const activeIndex = ref(0);

watchEffect(() => {
  dataBindForms.value = props.data.bindData || [];
});

const showConditionConfig = (index: number) => {
  activeIndex.value = index;
  console.log("dataBindForms.value[index]", dataBindForms.value[index]);

  conditionConfigModalRef.value?.show(dataBindForms.value[index].objDataBindInfo);
};

const showDataBindModal = (index: number) => {
  activeIndex.value = index;
  dataBindModalRef.value?.show(true);
};

const onDataBindModalUpdate = ({
  key,
  detailId
}: {
  key: string | null;
  detailId: string | null;
}) => {
  dataBindForms.value[activeIndex.value].key = key;
  dataBindForms.value[activeIndex.value].detailId = detailId;
  updateGroupAttribute();
};

const onConditionConfigUpdate = (val: IObjDataBindInfo[]) => {
  dataBindForms.value[activeIndex.value].objDataBindInfo = val;
  updateGroupAttribute();
};

const deleteDataBind = (index: number) => {
  dialog.warning({
    title: "警告",
    content: "确定删除当前数据吗？",
    positiveText: "确定",
    negativeText: "取消",
    maskClosable: false,
    closeOnEsc: false,
    onPositiveClick: async () => {
      dataBindForms.value?.splice(index, 1);
      updateGroupAttribute();
    },
    onAfterLeave: () => {}
  });
};

const updateGroupAttribute = async () => {
  if (!dataBindForms.value) return;
  const { nodes, links } = props.data;
  const mapId = mapStore.mapInfo!.mapId;

  await updateMapGroupData({
    ...props.data,
    mapId,
    bindData: dataBindForms.value,
    topoMapsGroupDataList: getGroupDataList(nodes, links)
  });

  window.$message.success("更新成功");
};

const onBindMapDataChange = () => {
  console.log("🚀 ~ onBindMapDataChange ~ dataBindForm.value:", dataBindForms.value);

  //   dataBindForms.value[key] = value;
  //   emit("onDataBindUpdate", dataBindForm.value);
};

const highlightEle = (item: IGroupDataBind) => {
  if (item.nodeLinkId) {
    highlightNodeLink(item.nodeLinkId);
  }
};
</script>

<style></style>
