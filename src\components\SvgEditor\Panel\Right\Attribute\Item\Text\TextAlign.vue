<template>
  <BaseItem title="对齐">
    <div class="flex flex-1">
      <n-button text style="font-size: 20px" class="mr-3" @click="alignText('left')">
        <n-icon>
          <AlignHorizontalLeftFilled />
        </n-icon>
      </n-button>
      <n-button text style="font-size: 20px" class="mr-3" @click="alignText('center')">
        <n-icon>
          <AlignHorizontalCenterFilled />
        </n-icon>
      </n-button>
      <n-button text style="font-size: 20px" class="mr-3" @click="alignText('end')">
        <n-icon>
          <AlignHorizontalRightFilled />
        </n-icon>
      </n-button>
    </div>
  </BaseItem>
</template>

<script setup lang="ts">
import type { ITextAlign } from "@/types";
import {
  AlignHorizontalCenterFilled,
  AlignHorizontalLeftFilled,
  AlignHorizontalRightFilled
} from "@/utils/components/icons";

import BaseItem from "../index.vue";

const emit = defineEmits<{
  "update:value": [value: ITextAlign];
}>();

defineProps<{
  value?: string | number | null;
}>();

const alignText = (align: ITextAlign) => {
  emit("update:value", align);
};
</script>

<style></style>
