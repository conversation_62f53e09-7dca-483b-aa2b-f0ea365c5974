<template>
  <BaseItem title="关键点">
    <!-- 使用表单编辑关键点 -->
    <n-scrollbar style="max-height: 200px">
      <div
        v-for="(item, index) in dataStore.currentLink?.points"
        :key="index"
        class="flex items-center mb-2"
      >
        <div class="flex-shrink-0 flex justify-center items-center w-6 h-full">
          {{ index + 1 }}
        </div>
        <n-input-number
          v-if="'x' in item"
          class="mr-2"
          v-model:value="item.x"
          size="small"
          :show-button="false"
          @update:value="updateLinkPoints"
        />
        <n-input-number
          v-if="'y' in item"
          v-model:value="item.y"
          size="small"
          :show-button="false"
          @update:value="updateLinkPoints"
        />

        <div class="w-28">
          <n-button text class="ml-2" @click="addPoint(index, item)">
            <n-icon>
              <Add />
            </n-icon>
          </n-button>

          <n-button
            v-if="!(index === 0)"
            text
            type="error"
            class="ml-2"
            @click="deletePoint(index)"
          >
            <n-icon>
              <Subtract />
            </n-icon>
          </n-button>
        </div>
      </div>
    </n-scrollbar>
  </BaseItem>
</template>

<script setup lang="ts">
import type { SVGCommand } from "svg-pathdata/dist/types";

import BaseItem from "@/components/SvgEditor/Panel/Right/Attribute/Item/index.vue";
import { useDataStore } from "@/stores";
import { Add, Subtract } from "@/utils/components/icons";

const emit = defineEmits<{
  "update:linkPoints": [];
}>();

const dataStore = useDataStore();

const addPoint = (index: number, item: SVGCommand) => {
  const point = { ...item };

  if ("x" in point) {
    point.x = point.x + 20;
  }
  dataStore.currentLink?.points.splice(index + 1, 0, point);
  emit("update:linkPoints");
};

const deletePoint = (index: number) => {
  dataStore.currentLink?.points.splice(index, 1);
  emit("update:linkPoints");
};

const updateLinkPoints = () => {
  emit("update:linkPoints");
};
</script>

<style></style>
