<template>
  <n-modal
    v-model:show="isVisible"
    preset="dialog"
    :title="title"
    size="huge"
    :bordered="false"
    :show-icon="false"
    :close-on-esc="false"
    :maskClosable="false"
    positive-text="确认"
    negative-text="取消"
    @positive-click="submit"
    @negative-click="hide"
    style="margin-top: 20vh; width: 400px"
  >
    <n-form
      ref="groupFormRef"
      :model="editScript"
      :rules="groupRules"
      label-placement="left"
      label-width="80"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="脚本名称" path="scriptName">
        <n-input v-model:value="editScript.scriptName" placeholder="脚本名称" />
      </n-form-item>
      <n-form-item label="应用状态" path="objName">
        <n-switch v-model:value="editScript.status" />
      </n-form-item>
      <n-form-item label="脚本内容" path="script">
        <n-button @click="setScript">{{ btnText }}</n-button>
      </n-form-item>
    </n-form>
  </n-modal>
  <CodeMirrorModal
    ref="codeMirrorModalRef"
    @onValueUpdate="onCodeMirrorValueUpdate"
  ></CodeMirrorModal>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import type { FormInst } from "naive-ui";

import CodeMirrorModal from "@/components/SvgEditor/Panel/Left/CodeMirror/index.vue";
import type { IScriptSource } from "@/types";
import { addScript, updateScript } from "@/utils/http/apis";

const isVisible = ref(false);
const isEdit = ref(false);
const groupRules = {
  scriptName: [{ required: true, message: "请输入脚本名称", trigger: "blur" }]
};
const title = computed(() => (isEdit.value ? "编辑对象" : "新增对象"));
const btnText = computed(() => (isEdit.value ? "查看" : "编辑"));

const groupFormRef = ref<FormInst | null>(null);
const codeMirrorModalRef = ref<InstanceType<typeof CodeMirrorModal> | null>(null);

let editScript = ref<IScriptSource>({
  id: 0,
  scriptName: "",
  mapId: "",
  script: "",
  status: false
});

const emits = defineEmits(["getScriptData"]);

const setScript = () => {
  codeMirrorModalRef.value?.show(editScript.value.script);
};

const show = async (val?: IScriptSource) => {
  if (val) {
    editScript.value = Object.assign({}, val);
  }
  isVisible.value = true;
  isEdit.value = val?.id ? true : false;
};

const finish = async () => {
  const msg = isEdit.value ? "更新成功" : "创建成功";
  hide();
  emits("getScriptData");
  window.$message.success(msg);
};

const submit = () => {
  groupFormRef.value?.validate((errors) => {
    if (errors) return;
    let newScriptData = Object.assign({}, editScript.value);

    if (isEdit.value) {
      let fn = updateScript;
      fn(newScriptData).then(() => {
        finish();
      });
    } else {
      let { id, ...newData } = newScriptData;
      let fn = addScript;
      fn(newData).then(() => {
        finish();
      });
    }
   
  });

  return false;
};

const hide = () => {
  isVisible.value = false;
};

const onCodeMirrorValueUpdate = (content: string) => {
  editScript.value.script = content;
};

defineExpose({
  show
});
</script>
