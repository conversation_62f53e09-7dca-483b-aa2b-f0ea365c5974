<template>
  <div class="fixed right-8 top-0 h-12 flex items-center">
    <div class="flex text-xs items-center mx-2">
      子节点： <n-switch class="ml-2" v-model:value="isShowChildren" size="small" />
    </div>
    <n-tooltip trigger="hover">
      <template #trigger>
        <n-button text style="font-size: 24px" class="mr-3" @click="saveSvg">
          <n-icon>
            <Save />
          </n-icon>
        </n-button>
      </template>
      保存
    </n-tooltip>
  </div>
  <div class="flex flex-col h-100vh">
    <div class="flex flex-1">
      <div class="w-50">
        <n-tree
          :checked-keys="checkedKeys"
          class="flex-shrink"
          :data="menuStore.menuList"
          expand-on-click
          checkable
          @update:checked-keys="onCheckedKeysChange"
        />
      </div>

      <n-dropdown
        trigger="manual"
        placement="bottom-start"
        :show="isMenuVisible"
        :options="AlignMenu"
        :x="position.x"
        :y="position.y"
        @select="onMenuSelect"
        @clickoutside="() => (isMenuVisible = false)"
        @contextmenu="($event: MouseEvent) => $event.preventDefault()"
      ></n-dropdown>

      <svg class="flex-1 bg-#101014" id="combineSvg" @contextmenu="onContextmenu">
        <g class="combine-group">
          <g
            v-for="(item, index) in svgs"
            :key="index"
            :id="`combineGroup_${item.mapId}`"
            class="combine-svg-item"
            :transform="`translate(${item.x} ${item.y})`"
          >
            <rect
              :width="item.size.width"
              :height="item.size.height"
              :fill="item.selected ? '#3f91e6a8' : '#5f9ea0a8'"
            ></rect>
            <svg :width="item.size.width" :height="item.size.height" v-if="isShowChildren">
              <path
                v-for="(link, index) in item.links"
                :key="index"
                :d="link.linkPath"
                :style="link.style"
              />
              <g
                v-for="(node, index) in item.nodes"
                :key="index"
                :transform="`translate(${node.x} ${node.y})`"
              >
                <rect :width="node.width" :height="node.height" :style="node.style"></rect>
                <!-- <text dx="5" dy="3" alignment-baseline="before-edge">{{ node.nodeText }}</text> -->
              </g>
            </svg>
          </g>
        </g>
      </svg>
    </div>
  </div>
  <CombineMapModal ref="combineMapModalRef" @onValueUpdate="onValueUpdate"></CombineMapModal>
</template>

<script setup lang="ts">
import { nextTick, onBeforeUnmount, onMounted, ref, toRef } from "vue";
import type { TreeOption } from "naive-ui";

import CombineMapModal from "@/components/Combine/Modal/CombineMap.vue";
import { useCommonStore, useMenuStore } from "@/stores";
import type { ICombineSvgData, IMapModel, IMapSource, IOriginalLink, IOriginalNode } from "@/types";
import { Save } from "@/utils/components/icons";
import { AlignMenu } from "@/utils/constant";
import { addMap, addNodeLinkList, getNodeLinkListByMapId } from "@/utils/http/apis";
import emitter from "@/utils/mitt";
import { bindCombineNodeEvent } from "@/utils/svg/combine/event/node";
import { bindCombineSvgZoom } from "@/utils/svg/combine/event/svg";
import { alignCombineSvg, getSvgNodesLinks, getSvgSize } from "@/utils/svg/combine/format";
import { formatLinks, formatNodes } from "@/utils/tools";

const menuStore = useMenuStore();
const commonStore = useCommonStore();

const svgs = ref<ICombineSvgData[]>([]);
const isShowChildren = ref(false);
const combineMapModalRef = ref<InstanceType<typeof CombineMapModal> | null>(null);

const checkedKeys = ref<string[]>([]);

menuStore.getMenuList();

const getSvgData = async (mapId: string, mapInfo: IMapSource) => {
  const { nodes, links } = await getNodeLinkListByMapId(mapId);
  const [width, height] = mapInfo.mapSize.split("*").map((item) => parseInt(item));
  const svgData = toRef({
    mapId,
    nodes: formatNodes(nodes),
    links: formatLinks(links),
    size: { width, height },
    x: 0,
    y: 0,
    tx: 0,
    ty: 0
  });
  svgs.value.push(svgData.value);

  nextTick(() => {
    bindCombineNodeEvent(mapId, svgData.value);
  });
};

const onCheckedKeysChange = (
  keys: string[],
  options: TreeOption[],
  meta: { node: TreeOption | null; action: "check" | "uncheck" }
) => {
  checkedKeys.value = keys;
  if (meta.action === "check") {
    getSvgData(meta.node!.key as string, meta.node!.raw as IMapSource);
  } else {
    const index = svgs.value.findIndex((item) => item.mapId === meta.node!.key);
    svgs.value.splice(index, 1);
  }
};

const initEvent = () => {
  emitter.on("on:ClearCombineSelected", () => {
    svgs.value.forEach((item) => {
      item.selected = false;
    });
  });
  emitter.on("on:CombineNodeClick", (mapId: string) => {
    svgs.value.forEach((item) => {
      if (item.mapId === mapId) {
        item.selected = !item.selected;
      }
    });
  });
};

onMounted(() => {
  bindCombineSvgZoom();
  initEvent();
});

onBeforeUnmount(() => {
  emitter.off("on:ClearCombineSelected");
  emitter.off("on:CombineNodeClick");
});

const saveSvg = () => {
  if (svgs.value.length === 0) {
    window.$message.error("请先选择图形");
    return;
  }

  combineMapModalRef.value?.show();
};

/**
 *
 * @param value
 *
 * @description 保存组合图
 */
const onValueUpdate = async (value: { mapName: string; menuId: string }) => {
  const svgInfo = getSvgSize();

  const map: IMapModel = {
    mapName: value.mapName,
    mapSize: `${svgInfo.width}*${svgInfo.height}`,
    background: "",
    menuId: value.menuId,
    mapIndex: 0,
    externalBind: {},
    internalBind: {},
    description: ""
  };

  commonStore.isLoading = true;
  const mapId = await addMap(map);
  const svgData = getSvgNodesLinks();

  const nodeList: IOriginalNode[] = [];
  const linkList: IOriginalLink[] = [];

  svgData.forEach((item) => {
    item.nodes.forEach((node) => {
      nodeList.push({
        ...node,
        sublayerList: node.sublayerList || [],
        mapId
      });
    });
    item.links.forEach((link) => {
      linkList.push({
        ...link,
        mapId,
        sublayerList: link.sublayerList || []
      });
    });
  });

  await addNodeLinkList({ nodeList, linkList });
  await menuStore.getMenuList();

  window.$message.success("保存成功");
  svgs.value = [];
  checkedKeys.value = [];
  commonStore.isLoading = false;
};

// 右键菜单
const isMenuVisible = ref(false);
const position = { x: 0, y: 0 };

const onContextmenu = (e: MouseEvent) => {
  isMenuVisible.value = true;
  e.preventDefault();
  e.stopPropagation();
  position.x = e.clientX;
  position.y = e.clientY;
};

const onMenuSelect = (key: string) => {
  const svgsSelected = svgs.value.filter((item) => item.selected);
  if (!svgsSelected.length) {
    window.$message.error("请先选择图形");
    return;
  }
  alignCombineSvg(key, svgsSelected);
  isMenuVisible.value = false;
};
</script>

<style scoped></style>
