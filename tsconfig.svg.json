{
  "compilerOptions": {
    "target": "es2020", // 指定ECMAScript目标版本，这里选择ES6
    "module": "esnext", // 使用ES6模块或者ESNext模块系统
    "strict": true, // 启用所有严格类型检查选项
    "esModuleInterop": true, // 允许默认导入和CommonJS/AMD模块一起工作
    "skipLibCheck": true, // 跳过对声明文件(.d.ts)的检查
    "forceConsistentCasingInFileNames": true // 强制文件名大小写一致性
  },
  "files": ["./src/utils/components/LogicCustomComponent.ts"], // 指定要编译的文件夹
  "exclude": ["node_modules"] // 排除node_modules文件夹
}
